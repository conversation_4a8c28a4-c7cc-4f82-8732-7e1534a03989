<template>
  <div>
    <div> 干员组干员 </div>
    <div class="my-5 mr-5">
      <ACard size="small" :class="`${prefixCls}__card`">
        <ATypographyParagraph
          :class="`${prefixCls}__card-content`"
          :ellipsis="ellipsis ? { rows: 1, onEllipsis: handleOnEllipsis } : {}"
          :content="formatUserList(memberList, '暂无')"
          class="mr-15"
        />
        <a-button
          v-if="showEllipsisButton"
          type="link"
          size="small"
          :class="`${prefixCls}__card-ellipsis-btn`"
          @click="ellipsis = !ellipsis"
        >
          {{ !ellipsis ? '收起' : '展开' }}
          <BasicArrow class="ml-1" :expand="ellipsis" up />
        </a-button>
      </ACard>
    </div>
    <div>干员组权限</div>
    <div>
      <!-- <BasicTable
        :data-source="permissionList"
        rowKey="ID"
        childrenColumnName="childrenColumnName"
        :class="`${prefixCls}__table`"
        @register="registerTable"
      >
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'children'">
            <ACheckboxGroup
              v-model:value="record.value"
              :class="`${prefixCls}__checkbox-group w-full`"
              :disabled="isAdmin === false || curGroupName === '管理员'"
              @change="checkboxChange"
            >
              <ARow :gutter="[8, 8]">
                <ACol
                  v-for="item in record.children"
                  :key="item.ID"
                  :xs="24"
                  :sm="24"
                  :md="12"
                  :lg="10"
                  :xl="8"
                  :xxl="4"
                >
                  <ACheckbox :value="item">
                    <ATypographyText
                      :content="item.description"
                      :ellipsis="{ tooltip: true }"
                      class="!max-w-[7vw]"
                    />
                  </ACheckbox>
                </ACol>
              </ARow>
            </ACheckboxGroup>
          </template>
        </template>
      </BasicTable> -->
      <BasicVxeTable :options="gridOptions" />
    </div>
  </div>
</template>

<script lang="tsx" setup>
import {
  Card as ACard,
  TypographyParagraph as ATypographyParagraph,
} from 'ant-design-vue';
import { computed, ref, unref } from 'vue';
import type {
  EditProjectPermissionParamsItem,
  ProjectPermissionListItem,
} from '/@/api/page/model/systemModel';
import {
  editProjectPermission,
  getProjectMemberListByPage,
  getProjectPermissionList,
} from '/@/api/page/system';
import type { UserInfoModel } from '/@/api/sys/model/userModel';
import { BasicArrow } from '/@/components/Basic';
import { useTable } from '/@/components/Table';
import { formatUserList } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { permissionColumns } from '/@/views/system/project/permission/permission.data';
import { useRouter } from 'vue-router';
import { BasicVxeTable } from '@hg-tech/oasis-common';
import ARow from 'ant-design-vue/es/grid/Row';
import tooltip from 'ant-design-vue/es/tooltip';
import { template } from 'lodash';
import ACheckboxGroup from '/@/components/VxeTable/src/components/ACheckboxGroup';

defineOptions({
  name: 'PermissionList',
});

const props = defineProps({
  curGroup: {
    type: String,
    default: '',
  },
  curGroupName: {
    type: String,
    default: '',
  },
  isAdmin: {
    type: Boolean,
    default: () => false,
  },
});

const { prefixCls } = useDesign('project-permission-list');
const { currentRoute } = useRouter();
const projectID = Number(currentRoute.value.params.id);
const permissionList = ref<ProjectPermissionListItem[]>([]);
const defaultSelectRowKeys = ref<number[]>([]);
const ellipsis = ref(true);
const showEllipsisButton = ref(false);
const memberList = ref<UserInfoModel[]>([]);

const gridOptions = computed(() => ({
  rowConfig: {
    keyField: 'id',
    isHover: true,
  },
  columns: [
    { type: 'checkbox', width: 60 },
    { field: 'name', title: '类型', width: 200 },
    {
      title: '权限内容',
      field: 'children',
      align: 'left',
      slots: {
        default({ row ,column}) {
          return <template v-if="column.dataIndex === 'children'">
            <ACheckboxGroup
              v-model:value="record.value"
              :class="`${prefixCls}__checkbox-group w-full`"
              :disabled="isAdmin === false || curGroupName === '管理员'"
              @change="checkboxChange"
            >
              <ARow :gutter="[8, 8]">
                <ACol
                  v-for="item in record.children"
                  :key="item.ID"
                  :xs="24"
                  :sm="24"
                  :md="12"
                  :lg="10"
                  :xl="8"
                  :xxl="4"
                >
                  <ACheckbox :value="item">
                    <ATypographyText
                      :content="item.description"
                      :ellipsis="{ tooltip: true }"
                      class="!max-w-[7vw]"
                    />
                  </ACheckbox>
                </ACol>
              </ARow>
            </ACheckboxGroup>
          </template>
        },
      },
    },
  ],
  data: permissionList.value,
  striped: false,
}));

async function getMemberList() {
  const { list } = await getProjectMemberListByPage(
    projectID,
    unref(props.curGroup),
    {
      page: 1,
      pageSize: 999,
    },
  );

  if (list?.length > 0) {
    memberList.value = list;
  } else {
    memberList.value = [];
  }
}

getMemberList();

const [registerTable, { getDataSource, setSelectedRowKeys }] = useTable({
  columns: permissionColumns,
  striped: false,
  useSearchForm: false,
  showTableSetting: false,
  showIndexColumn: false,
  clickToRowSelect: false,
  pagination: false,
  rowSelection: {
    type: 'checkbox',
    onSelectAll,
    onSelect,
    getCheckboxProps() {
      // 管理员组默认全选，故禁用勾选，非管理员查看也禁用功能
      if (unref(props.isAdmin) === false || unref(props.curGroupName) === '管理员') {
        return { disabled: true };
      } else {
        return { disabled: false };
      }
    },
  },
  canResize: false,
});

// 获取当前角色组权限列表
async function getList() {
  const { apis } = await getProjectPermissionList(
    projectID,
    unref(props.curGroup),
  );
  apis.forEach((e, i) => {
    e.value = [];
    e.ID = i;

    let selectCount = 0;

    e.children?.forEach((child, i) => {
      child.value = child;

      if (child.checked && child.ID) {
        e.value?.push(child);
        selectCount++;
      }

      if (e.children && i === e.children.length - 1) {
        if (selectCount === e.children.length) {
          defaultSelectRowKeys.value.push(e.ID as number);
        }

        selectCount = 0;
      }

      // setSelectedRowKeys(unref(defaultSelectRowKeys));
    });
  });
  permissionList.value = apis;
}

getList();

/**
 * 列表选中事件
 * @param record 选择的行数据
 * @param selected 选择状态
 */
function onSelect(record, selected: boolean) {
  const dataSource = unref(getDataSource());

  dataSource[record.ID].value = [];

  if (selected) {
    dataSource[record.ID].children.forEach((item) => {
      dataSource[record.ID].value.push(item);
    });
  }

  editPermission();
}

/**
 * 列表全选中事件
 * @param selected 选择状态
 */
function onSelectAll(selected: boolean) {
  const dataSource = unref(getDataSource());

  dataSource.forEach((e) => {
    e.value = [];

    if (selected) {
      dataSource[e.ID].children.forEach((item) => {
        dataSource[e.ID].value.push(item);
      });
    }
  });
  editPermission();
}

/**
 * 单个权限变更事件
 */
function checkboxChange() {
  editPermission();
}

// 选择有变化调用编辑权限接口
async function editPermission() {
  const dataSource = unref(getDataSource());
  let submitList: EditProjectPermissionParamsItem[] = [];

  dataSource.forEach((e) => {
    const list: EditProjectPermissionParamsItem[] = [];

    e.value?.forEach((item) => {
      const { method, path } = item;

      list.push({ method, path });
    });
    submitList = submitList.concat(list);
  });

  await editProjectPermission(projectID, unref(props.curGroup), {
    casbin_infos: submitList,
  });
}

function handleOnEllipsis(ellipsis) {
  showEllipsisButton.value = ellipsis;
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-project-permission-list';
.@{prefix-cls} {
  &__card {
    background-color: @FO-Container-Fill2;
    position: relative;

    &-content {
      margin-bottom: 0 !important;
    }

    &-ellipsis-btn {
      position: absolute;
      right: 5px;
      bottom: 11px;
    }
  }

  &__table {
    .ant-table-row-selected > td.ant-table-cell {
      background: none !important;
    }
  }
}
</style>
