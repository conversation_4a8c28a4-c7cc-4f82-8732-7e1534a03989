<template>
  <div class="flex-1 overflow-hidden">
    <EditTable
      v-if="perforceAccessLevelList?.length > 0 && curServer"
      :key="tableKey"
      ref="editTableRef"
      :isSystemOnly="isSystemOnly"
      :scenarioID="curScenario"
      :scenarioList="scenarioList"
      :dataLoading="loading"
      :perforcePermissionList="perforcePermissionList"
      @dataSave="handleDataSave"
      @filterChange="handleFilterChange"
      @needAddScenario="handleSaveAddScenario"
      @serverChange="handleServerChange"
    />
    <div v-else class="m-3 text-lg c-FO-Functional-Error1-Default font-bold">
      请先进行权限类别配置
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, watch } from 'vue';
import EditTable from './EditTable.vue';
import type {
  PerforcePermissionsListItem,
  PerforceScenariosListItem,
} from '/@/api/page/model/perforceModel';
import {
  addPerforceScenarios,
  getPerforcePermissionsListByPage,
  getPerforceScenariosListByPage,
} from '/@/api/page/perforce';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { buildUUID } from '/@/utils/uuid';
import { usePerforceAccessLevel, usePerforceServer } from '/@/views/versionControl/perforceManagement/hook';
import { useLatestPromise } from '@hg-tech/utils-vue';

const userStore = useUserStoreWithOut();
const curScenario = ref<number>();
const scenarioList = ref<PerforceScenariosListItem[]>([]);
const perforcePermissionList = ref<PerforcePermissionsListItem[]>([]);
const tableKey = ref<string>('');
const isSystemOnly = ref<boolean>(false);
const { createMessage } = useMessage();
const editTableRef = ref();
const { curServer, getPerforceServerList } = usePerforceServer();
const { getAccessLevelList, perforceAccessLevelList } = usePerforceAccessLevel();
const { execute, loading } = useLatestPromise(getPerforcePermissionsListByPage);
// 获取场景列表
async function getScenariosList(id?: number, systemOnly?: boolean) {
  const { list } = await getPerforceScenariosListByPage(0, 0, {
    page: 1,
    pageSize: 999,
    systemOnly,
  });
  if (list?.length > 0) {
    scenarioList.value = list;
    const curActive = list.find((e) => e.activate);
    curScenario.value = id || curActive?.ID || list[0]?.ID;
  } else {
    scenarioList.value = [];
    curScenario.value = undefined;
  }
}

// 获取权限列表
async function getPermissionList(systemOnly?: boolean, getNewKey = true) {
  // 每次获取表格数据前刷新组件，以修复拖动到最后一行会多一行假数据的bug
  getNewKey && (tableKey.value = buildUUID());
  const { list } = await execute(0, 0, {
    page: 1,
    pageSize: 999,
    systemOnly,
    // 系统页面的全部分类仅显示激活的
    activateOnly: !systemOnly,
    serverID: curServer.value?.ID,
  });
  if (list?.length > 0) {
    const sortList = list.sort((a, b) => (a.sort || 0) - (b.sort || 0));
    // 系统页面显示全部时再根据项目id排序一次
    if (!systemOnly) {
      sortList.sort((a, b) => (b.projectID || 0) - (a.projectID || 0));
    }
    perforcePermissionList.value = sortList;
  } else {
    perforcePermissionList.value = [];
  }
}

async function init() {
  await getAccessLevelList();
  await getPerforceServerList();
  await getScenariosList();
  await getPermissionList();
}

onBeforeMount(() => {
  init();
});

// 保存发现没场景，需新增场景后保存
async function handleSaveAddScenario() {
  await addPerforceScenarios(0, 0, {
    name: '日常开发',
    description: '日常开发权限场景',
  });
  await getScenariosList();
  createMessage.warn('场景不存在，已自动创建场景');
  editTableRef.value.handleSave();
}

async function handleDataSave(systemOnly = false, getNewKey = true, afterSave?: () => void) {
  await getPermissionList(systemOnly, getNewKey);
  afterSave && afterSave();
}

function handleFilterChange(systemOnly: boolean) {
  getScenariosList(undefined, systemOnly);
  getPermissionList(systemOnly);
  isSystemOnly.value = systemOnly;
}

function handleServerChange() {
  getPermissionList(isSystemOnly.value);
}

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      init();
    }
  },
);
</script>
