import { createRouter, createWebHistory } from 'vue-router';
import { PlatformEnterPoint, PlatformRoutePath } from '@hg-tech/oasis-common';
import { PermissionPoint } from '../constants/permission.ts';
import type { App } from 'vue';
import { withPermission } from './modules/withPermission.ts';

export const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      name: PlatformEnterPoint.BannerManagement,
      path: PlatformRoutePath.BannerManagement,
      component: () => import('../views/page/BannerManagement/Entry.vue'),
      meta: {
        title: '首页 Banner',
        permissionDeclare: {
          any: [PermissionPoint.BannerManagement],
        },
      },
    },
    {
      name: PlatformEnterPoint.ProductionNews,
      path: PlatformRoutePath.ProductionNews,
      component: () => import('../views/page/ProductionNews/Entry.vue'),
      meta: {
        title: '产品动态',
        permissionDeclare: {
          any: [PermissionPoint.ProductionNews],
        },
      },
    },
    {
      name: PlatformEnterPoint.ProjectSettings,
      path: PlatformRoutePath.ProjectSettings,
      component: () => import('../views/page/ProjectSettings/Entry.vue'),
      meta: {
        title: '项目配置',
        permissionDeclare: {
          any: [PermissionPoint.ProjectSettings],
        },
      },
    },
    {
      path: '/:error(.*)*',
      component: () => import('../views/Redirect.vue'),
    },
  ],
  strict: true,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

export function setupRouter(app: App<Element>) {
  app.use(router);

  // with plugins
  withPermission(router);
}
