import { type RuleV1ExcludeMergeType, RuleV1FileNameRuleType } from '@hg-tech/api-schema-merge';
import { FormItem, Input, Select } from 'ant-design-vue';
import { ExcludeMergeOptions, FileNameRuleOptions } from '../../../models/config.model';
import { type PropType, computed, defineComponent } from 'vue';

const ExcludeRuleItem = defineComponent({
  props: {
    domain: {
      type: String as PropType<string>,
      required: true,
    },
    index: {
      type: Number as PropType<number>,
      required: true,
    },
    ruleType: {
      type: String as PropType<RuleV1FileNameRuleType>,
    },
    pattern: {
      type: String as PropType<string>,
    },
    excludeMergeType: {
      type: String as PropType<RuleV1ExcludeMergeType>,
    },
  },
  emits: {
    'update:ruleType': (value?: RuleV1FileNameRuleType) => true,
    'update:pattern': (value?: string) => true,
    'update:excludeMergeType': (value?: RuleV1ExcludeMergeType) => true,
  },
  setup(props, { emit }) {
    const ruleType = computed({
      get: () => props.ruleType,
      set: (value) => {
        emit('update:ruleType', value);
      },
    });

    const pattern = computed({
      get: () => props.pattern,
      set: (value) => {
        emit('update:pattern', value);
      },
    });
    const excludeMergeType = computed({
      get: () => props.excludeMergeType,
      set: (value) => {
        emit('update:excludeMergeType', value);
      },
    });

    const rules = {
      ruleType: [{ required: true, message: '请选择规则类型' }],
      pattern: [
        {
          required: true,
          message: '请输入文件名或正则表达式',
        },
        {
          validator(_: unknown, value: string) {
            try {
              const r = new RegExp(value);
              return Promise.resolve();
            } catch {
              return Promise.reject(new Error('请输入合法的正则表达式'));
            }
          },
        },
      ],
      excludeMergeType: [{ required: true, message: '请选择忽略方式' }],
    };

    return () => (
      <div class="w-full flex gap-12px">
        <FormItem>
          若
        </FormItem>
        <FormItem class="min-w-120px" name={[props.domain, props.index, 'ruleType']} rules={rules.ruleType}>
          <Select placeholder="请选择规则类型" v-model:value={ruleType.value}>
            {FileNameRuleOptions.map((rule) => (
              <Select.Option key={rule.value} value={rule.value}>
                {rule.label}
              </Select.Option>
            ))}
          </Select>
        </FormItem>
        <FormItem class="min-w-60px flex-1" name={[props.domain, props.index, 'pattern']} rules={rules.pattern}>
          <Input
            placeholder={ruleType.value === RuleV1FileNameRuleType.REGEX ? '请输入正则表达式' : '请输入匹配文本'}
            v-model:value={pattern.value}
          />
        </FormItem>
        <FormItem>
          则
        </FormItem>
        <FormItem class="min-w-160px" name={[props.domain, props.index, 'excludeMergeType']} rules={rules.excludeMergeType}>
          <Select
            options={ExcludeMergeOptions}
            placeholder="请选择忽略方式"
            v-model:value={excludeMergeType.value}
          />
        </FormItem>
      </div>
    );
  },
});

export {
  ExcludeRuleItem,
};
