<template>
  <Upload
    :fileList="fileList"
    :accept="accept"
    :multiple="uploadMaxCount > 1"
    listType="picture-card"
    :beforeUpload="beforeUpload"
    :customRequest="handleUpload"
    @change="handleChange"
    @preview="handlePreview"
  >
    <div v-if="fileList.length < uploadMaxCount">
      <LoadingOutlined v-if="loading" />
      <PlusOutlined v-else />
      <div class="ant-upload-text">
        <slot>上传</slot>
      </div>
    </div>
  </Upload>
</template>

<script lang="ts" setup>
import { computed, ref, shallowRef, watch } from 'vue';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { type UploadChangeParam, type UploadFile, type UploadProps, message, Upload } from 'ant-design-vue';
import { forgeonAdminApi } from '../api';
import { isEqual } from 'lodash';
import { safeToArray } from '@hg-tech/utils';
import { requestService } from '../services/req.ts';
import { previewImage } from '../utils/image.ts';

const props = withDefaults(defineProps<{
  accept?: string;
  /**
   * 文件唯一标识
   */
  url: undefined | string | string[];
  /**
   * 文件预览地址
   */
  previewMap?: { [url: string]: string };
  maxCount?: number;
  maxSize?: number;// KB
}>(), {
  maxCount: 1,
});

const emit = defineEmits<{
  (e: 'update:url', v: string | string[]): void;
  (e: 'change', v: string | string[]): void;
}>();

type UploaderFileItem = UploadFile<{ id: string }>;

const loading = ref<boolean>(false);
const _PreviewMap = shallowRef<Record<string, string | undefined>>({});
const cachedPreviewMap = computed(() => ({ ..._PreviewMap.value, ...props.previewMap }));
const isMultipleInput = computed(() => Array.isArray(props.url));
const uploadMaxCount = computed(() => (isMultipleInput.value ? Math.max(props.maxCount, 1) : 1));
const inputFileList = computed(() => safeToArray(props.url).map((u) => ({
  uid: u,
  name: u,
  status: 'done',
  url: cachedPreviewMap.value?.[u] ?? u,
} as UploaderFileItem)));
const fileList = ref<UploaderFileItem[]>([...inputFileList.value]);

watch(fileList, (files) => {
  const doneIds = files.filter((file) => file.status === 'done').map((file) => file.response?.id as string);
  if (!isEqual(doneIds, props.url)) {
    // 上传成功部分不匹配，更新 props
    const emitUrl = isMultipleInput.value ? doneIds : doneIds[0];
    emit('update:url', emitUrl);
    emit('change', emitUrl);
  }
});

function handleChange(info: UploadChangeParam<UploaderFileItem>) {
  switch (info.file.status) {
    case 'uploading':
      loading.value = true;
      break;
    case 'done':
      loading.value = false;
      if (info.file.originFileObj && info.file.response?.id) {
        _PreviewMap.value = {
          ..._PreviewMap.value,
          [info.file.response.id]: info.file.thumbUrl,
        };
      }
      break;
    case 'error':
      loading.value = false;
      message.error('上传失败');
      break;
  }
  // 过滤掉不合法的文件
  fileList.value = info.fileList.filter((i) => i.status);
}

async function beforeUpload(file: UploaderFileItem) {
  if (props.maxSize && (!file.size || file.size > props.maxSize * 1024)) {
    message.error(`文件大小不能大于 ${props.maxSize}K`);
    throw new Error('file size too large');
  }
}

const handleUpload: UploadProps['customRequest'] = async ({ onError, onProgress, onSuccess, file, headers }) => {
  const uploadFile = file as File;
  const res = await forgeonAdminApi.api.fileServiceUploadFileStsSignature({}, { contentType: uploadFile.type });

  const uploadId = res.data?.data?.fileName;
  const uploadUrl = res.data?.data?.url;

  if (uploadUrl) {
    requestService.request(uploadUrl, {
      method: 'PUT',
      headers: {
        ...headers,
        'Content-Type': uploadFile.type,
      },
      data: uploadFile,
      onUploadProgress(progressEvent) {
        onProgress?.({ percent: (progressEvent.loaded / progressEvent.total!) * 100 });
      },
    }).then(() => {
      onSuccess?.({ id: uploadId } as UploaderFileItem['response']);
    }, onError);
  } else {
    onError?.(new Error('获取上传地址失败'));
  }
};

function handlePreview(file: UploaderFileItem) {
  if (file.url) {
    previewImage({
      images: [cachedPreviewMap.value?.[file.url] ?? file.url],
    });
  }
}
</script>
