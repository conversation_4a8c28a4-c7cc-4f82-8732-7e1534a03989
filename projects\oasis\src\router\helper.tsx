import { Exception } from '../views/sys/exception';
import { defineComponent, h, watch } from 'vue';
import { Spin } from 'ant-design-vue';
import { LoadSubSysStatus, useLoadSubSys } from '../store/modules/loadSubSys.ts';
import { type RouteMeta, type RouteRecordRaw, useRouter } from 'vue-router';
import microApp from '@micro-zoe/micro-app';
import { debounce } from 'lodash';
import type { MicroAppConfig } from '@micro-app/types';

interface SubSysRouteConfig {
  /**
   * 子应用名称，需要保障唯一性
   */
  name: string;
  /**
   * 基础路径，请以 / 开头
   */
  basePath: `/${string}`;
  /**
   * 子应用访问路径，应从环境变量中获取
   */
  url: string;
  /**
   * 子应用标题
   */
  title?: string;
  /**
   * 子应用配置
   */
  configs?: MicroAppConfig;
  /**
   * 路由元信息
   */
  routeMeta?: Omit<RouteMeta, 'microApp'>;
  /**
   * 是否自行在子应用中上报埋点
   * @default true
   */
  selfTrack?: boolean;
}

// fixme: 公共query需要抽出枚举trackerGuard.ts
function cleanPath(path: string) {
  const url = new URL(path, location.origin);
  url.searchParams.delete('p');
  url.searchParams.delete('fs');
  url.searchParams.delete('oasis');
  return url.pathname + url.search;
}

export function toSubSysRoute(sysConfig: SubSysRouteConfig): RouteRecordRaw {
  return {
    name: sysConfig.name,
    path: `${sysConfig.basePath}/:path(.*)*`,
    component: defineComponent({
      setup() {
        const subSysStore = useLoadSubSys();
        const router = useRouter();

        watch(() => sysConfig.name, (sysName, prvName, onCleanup) => {
          // 同步当前子路由变化到主路由
          const offAfterRoute = microApp.router.afterEach((to) => {
            if (router.currentRoute.value.name === sysName && cleanPath(router.currentRoute.value.fullPath) !== cleanPath(to.fullPath)) {
              router.replace(to.fullPath);
            }
          });

          onCleanup(() => {
            offAfterRoute();
            microApp.unmountApp(sysName, { destroy: true });
          });
        }, { immediate: true });

        // 同步主路由变化到子应用
        const replaceMicroAppRouter = debounce((to: Parameters<typeof microApp.router.replace>[0]) => {
          const sysPath = microApp.router.current.get(sysConfig.name)?.fullPath;
          if (cleanPath(to.path) !== cleanPath(sysPath)) {
            return microApp.router.replace(to);
          }
        }, 100);
        watch(router.currentRoute, (route) => {
          if (route.meta.microApp?.name !== sysConfig.name) {
            // 如果当前路由不是当前子应用的路由，则不处理
            return;
          }

          replaceMicroAppRouter({
            name: sysConfig.name,
            path: route.fullPath.match(new RegExp(`^${sysConfig.basePath}(.*)`))?.[0] ?? '/',
          });
        });

        // 渲染回退内容
        function renderFallbackContent() {
          switch (subSysStore.status[sysConfig.name]) {
            case LoadSubSysStatus.Loaded:
              return null;
            case LoadSubSysStatus.Error:
              return h(
                'div',
                { class: 'flex items-center justify-center h-full' },
                h(Exception, {
                  status: '500',
                  title: `${sysConfig.title || sysConfig.name} 加载失败`,
                } as any, []),
              );
            case LoadSubSysStatus.Loading:
            default:
              return h(
                'div',
                { class: 'flex items-center justify-center h-full' },
                h(Spin, { spinning: true, size: 'large' }),
              );
          }
        }

        return () => (
          <div class="h-full">
            {
              h('micro-app', {
                'style': { display: renderFallbackContent() ? 'none' : 'block' },
                'iframe': true,
                'name': sysConfig.name,
                'router-mode': 'native',
                'url': sysConfig.url,
                ...sysConfig.configs,
              })
            }
            {renderFallbackContent()}
          </div>
        );
      },
    }),
    meta: {
      permissionDeclare: {}, // 进入router守卫的auth判断中
      ...sysConfig.routeMeta,
      title: sysConfig.title ?? sysConfig.name,
      microApp: {
        name: sysConfig.name,
        url: sysConfig.url,
        configs: sysConfig.configs,
        title: sysConfig.title,
        selfTrack: sysConfig.selfTrack ?? true,
      },
    },
  };
}
