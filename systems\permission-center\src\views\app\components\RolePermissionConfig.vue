<template>
  <div class="h-full flex flex-col gap-20px">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="flex items-center justify-center py-40px">
      <Spin size="large" />
    </div>

    <!-- 内容区域 -->
    <template v-else>
      <!-- 顶部信息行 -->
      <div class="flex items-center justify-between px-16px">
        <div class="flex items-center gap-8px">
          <!-- 角色名称 -->
          <div class="FO-Font-B14 c-FO-Content-Text2">
            {{ groupDetail?.name || '普通成员' }}
          </div>

          <!-- 成员统计 -->
          <MemberCountDisplay :memberCount="memberCount" />
        </div>

        <!-- 权限统计 -->
        <div class="FO-Font-B14 c-FO-Content-Text3">
          已配置 {{ selectedPermissionCount }} 项权限
        </div>
      </div>

      <!-- 权限配置区域 -->
      <div class="min-h-0 flex-1 overflow-y-auto px-16px">
        <div class="flex flex-col gap-20px">
          <div v-for="category in categoriesWithPermissions" :key="category.id" class="flex flex-col gap-6px">
            <!-- 权限分类标题行 -->
            <div class="flex items-center">
              <Checkbox
                :checked="category.isAllSelected" :indeterminate="category.isIndeterminate"
                @change="handleCategoryChange(category, $event)"
              >
                <span class="FO-Font-B14 c-FO-Content-Text1">{{ category.name }}</span>
              </Checkbox>
            </div>

            <!-- 权限项列表 -->
            <div class="rounded-8px bg-FO-Container-Fill2 px-20px py-12px">
              <div v-if="category.resources.length === 0" class="py-20px text-center c-FO-Content-Text3">
                暂无权限项
              </div>
              <div v-else class="grid grid-cols-3 gap-x-8px gap-y-12px">
                <div v-for="permission in category.resources" :key="permission.id" class="flex items-center">
                  <Checkbox
                    :checked="permission.selected"
                    @change="handlePermissionChange(category, permission, $event)"
                  >
                    <span class="FO-Font-R14">{{ permission.name }}</span>
                  </Checkbox>
                  <Tooltip v-if="permission.description" :title="permission.description">
                    <FillHelp class="cursor-pointer text-16px c-FO-Content-Icon3" />
                  </Tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="categoriesWithPermissions.length === 0" class="h-full flex-1 overflow-hidden">
          <div class="h-full flex items-center justify-center">
            <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" class="c-FO-Content-Text4">
              <template #description>
                无权限数据，去
                <Button type="link" class="px-0" @click="handleAddPermission">
                  添加权限
                </Button>
              </template>
            </Empty>
          </div>
        </div>
      </div>

      <div
        v-if="hasChanges"
        class="sticky bottom-0 border-t border-FO-Container-Stroke1 bg-FO-Container-Fill1 px-20px pt-16px"
      >
        <div class="flex items-center justify-end gap-12px">
          <div class="flex items-center gap-8px rounded-6px bg-FO-Functional-Warning2-Default px-12px py-4px">
            <WarningErrorIcon class="c-FO-Functional-Warning1-Default" />
            <span class="FO-Font-R14 c-FO-Functional-Warning1-Default">当前有未保存数据</span>
          </div>

          <Button class="btn-fill-default w-100px" @click="handleCancel">
            取消
          </Button>
          <Button class="btn-fill-primary w-100px" :loading="updatingPermissions" @click="handleSave">
            保存
          </Button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { Button, Checkbox, Empty, message, Modal, Spin, Tooltip } from 'ant-design-vue';
import type { PermissionAppGroupListItem } from '../../../api/group.ts';
import { fetchPermissionGroupDetail, fetchPermissionGroupDetailV2, updatePermissionGroupResource } from '../../../api/group.ts';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { useRouter } from 'vue-router';
import WarningErrorIcon from '../../../assets/icons/fill-warning-error.svg?component';
import type { PermissionTenantInfo } from 'src/api/app.ts';
import MemberCountDisplay from './MemberCountDisplay.vue';
import { PlatformEnterPoint, usePreventLeavePage } from '@hg-tech/oasis-common';
import FillHelp from '../../../assets/icons/fill-help.svg?component';
import { usePermissionCategories } from '../../../composables/usePermissionCategories.ts';

const props = defineProps<{
  activeGroupId?: PermissionAppGroupListItem['id'];
  tenantId?: PermissionTenantInfo['id'];
  appId?: number;
}>();

const router = useRouter();

// 本地权限选择状态（用于修改态）
const localSelectedPermissionIds = ref<number[]>([]);

// 使用权限分类 composable
const { categoriesWithUncategorized, loading: loadingCategories, refreshData } = usePermissionCategories();

// 获取权限组详情
const { data: groupDetailRes, loading: loadingGroupDetail, execute: fetchGroupDetail } = useLatestPromise(fetchPermissionGroupDetailV2);
// 获取权限组权限详情
const { data: groupPermissionDetailRes, loading: loadingGroupPermissionDetail, execute: fetchGroupPermissionDetail } = useLatestPromise(fetchPermissionGroupDetail);

// 更新权限组权限
const { loading: updatingPermissions, execute: updatePermissions } = useLatestPromise(updatePermissionGroupResource);

// 计算加载状态
const isLoading = computed(() => loadingCategories.value || loadingGroupDetail.value || loadingGroupPermissionDetail.value);

const groupDetail = computed(() => groupDetailRes.value?.data?.data || {});
const groupPermissionDetail = computed(() => groupPermissionDetailRes.value?.data?.data || {});

// 计算成员统计 - 使用后端返回的计数字段
const memberCount = computed(() => {
  const detail = groupDetail.value;

  return {
    users: detail?.memberCnt || 0,
    departments: detail?.orgCnt || 0,
    groups: detail?.groupCnt || 0,
  };
});

// 当前服务器端的权限ID列表
const serverPermissionIds = computed(() => groupPermissionDetail.value?.resources || []);

// 是否有修改（本地状态与服务器状态不一致）
const hasChanges = computed(() => {
  const local = [...localSelectedPermissionIds.value].sort();
  const server = [...serverPermissionIds.value].sort();
  return JSON.stringify(local) !== JSON.stringify(server);
});

// 处理权限分类数据，添加选中状态
const categoriesWithPermissions = computed(() => {
  return categoriesWithUncategorized.value.map((category) => {
    const resources = (category.resources || []).map((resource) => ({
      ...resource,
      selected: resource.id ? localSelectedPermissionIds.value.includes(resource.id) : false,
    }));

    const selectedCount = resources.filter((r) => r.selected).length;
    const totalCount = resources.length;

    return {
      ...category,
      resources,
      isAllSelected: totalCount > 0 && selectedCount === totalCount,
      isIndeterminate: selectedCount > 0 && selectedCount < totalCount,
    };
  });
});

// 计算已选择的权限总数
const selectedPermissionCount = computed(() => {
  return categoriesWithPermissions.value.reduce((total, category) => {
    return total + category.resources.filter((r) => r.selected).length;
  }, 0);
});

// 监听activeGroupId变化，重新获取数据
watch(() => [props.activeGroupId, props.tenantId], ([newGroupId, newTenantId]) => {
  if (newGroupId) {
    fetchGroupDetail({ appId: props.appId, groupId: newGroupId, tenantId: newTenantId }, {});
    fetchGroupPermissionDetail({ appId: props.appId, groupId: newGroupId, tenantId: newTenantId }, {});
  }
}, { immediate: true });

// 监听appId变化，获取权限分类
watch(() => props.appId, (newAppId) => {
  if (newAppId) {
    refreshData(newAppId);
  }
}, { immediate: true });

// 监听服务器权限数据变化，同步到本地状态
watch(serverPermissionIds, (newPermissionIds) => {
  localSelectedPermissionIds.value = newPermissionIds.filter((id): id is number => id !== undefined);
}, { immediate: true });

// 处理分类复选框变化
function handleCategoryChange(category: any, event: any) {
  const checked = event.target.checked;
  const newSelectedIds = [...localSelectedPermissionIds.value];

  category.resources.forEach((resource: any) => {
    if (!resource.id) {
      return;
    }
    const index = newSelectedIds.indexOf(resource.id);
    if (checked && index === -1) {
      newSelectedIds.push(resource.id);
    } else if (!checked && index !== -1) {
      newSelectedIds.splice(index, 1);
    }
  });

  localSelectedPermissionIds.value = newSelectedIds;
}

// 处理单个权限变化
function handlePermissionChange(_category: any, permission: any, event: any) {
  const checked = event.target.checked;
  const newSelectedIds = [...localSelectedPermissionIds.value];

  if (!permission.id) {
    return;
  }
  const index = newSelectedIds.indexOf(permission.id);

  if (checked && index === -1) {
    newSelectedIds.push(permission.id);
  } else if (!checked && index !== -1) {
    newSelectedIds.splice(index, 1);
  }

  localSelectedPermissionIds.value = newSelectedIds;
}

// 实际保存权限更改的逻辑
async function doSave() {
  if (!props.activeGroupId || !props.appId) {
    return;
  }

  try {
    const res = await updatePermissions(
      { appId: props.appId, groupId: props.activeGroupId, tenantId: props.tenantId },
      localSelectedPermissionIds.value,
    );

    if (res?.data?.code === 0) {
      // 更新成功后重新获取权限组详情
      await fetchGroupPermissionDetail({ appId: props.appId, groupId: props.activeGroupId, tenantId: props.tenantId }, {});

      message.success('权限更新成功');
    }
  } catch (error) {
    console.error('更新权限失败:', error);
    message.error('权限更新失败');
  }
}

// 保存权限更改（包含公共角色确认）
async function handleSave() {
  // 检查是否为公共角色
  if (groupDetail.value?.isPublic) {
    Modal.confirm({
      icon: null,
      width: 496,
      okText: '确认',
      okButtonProps: {
        type: 'primary',
      },
      cancelText: '取消',
      cancelButtonProps: {
        // @ts-expect-error cancelButtonProps支持class但没有类型定义
        class: 'btn-fill-default',
      },
      centered: true,
      closable: true,
      title: () => {
        return (
          <div class="flex items-center">
            <WarningErrorIcon class="c-FO-Functional-Warning1-Default" />
            <div class="FO-Font-B16 ml-8px"> 保存权限配置 </div>
          </div>
        );
      },
      content: `角色【${groupDetail.value?.name}】为公共角色，当前操作将对所有项目内的该角色生效，是否继续保存？`,
      onOk() {
        return doSave();
      },
    });
  } else {
    // 非公共角色直接保存
    await doSave();
  }
}

// 取消更改，恢复到服务器状态
function handleCancel() {
  localSelectedPermissionIds.value = serverPermissionIds.value.filter((id): id is number => id !== undefined);
}

function handleAddPermission() {
  router.push({
    name: PlatformEnterPoint.PermissionCenterManagement,
    params: {
      appId: props.appId,
    },
    query: {
      tenantId: props.tenantId,
    },
  });
}

usePreventLeavePage(hasChanges, {
  title: '数据暂未保存',
  content: '角色权限配置发生更改，退出后设置项会丢失，要保存吗？',
  okText: '留在当前',
  cancelText: '退出',
  okFunc: () => {
    return Promise.resolve(false);
  },
  cancelFunc: () => {
    return Promise.resolve(true);
  },
});
</script>
