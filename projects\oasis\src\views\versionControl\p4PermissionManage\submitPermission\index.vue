<template>
  <PageWrapper
    v-if="!isLoading"
    :class="prefixCls"
    :title="`提交权限：${streamName}`"
    headerSticky
    @back="goBack"
  >
    <template #subTitle>
      <div class="mt-2 flex items-center">
        <div class="ml-3 text-xs">
          为分支配置提交权限，提交工具会依照配置判断干员是否可提交
        </div>
        <Button
          v-if="curStream?.permissionStatus && !isCloneConfig"
          class="custom-rounded-btn ml-4"
          borderColor="black"
          noIcon
          size="small"
          @click="handleDelete"
        >
          删除提交权限配置
        </Button>
      </div>
    </template>
    <template #extra>
      <template v-if="curStream?.permissionStatus && !isCloneConfig">
        <Button
          v-track="'2xlqp0ytnz'"
          size="small"
          class="custom-rounded-btn"
          borderColor="black"
          noIcon
          @click="handleSubmitWhiteList"
        >
          提交白名单
        </Button>
      </template>
    </template>
    <div v-if="curStream?.permissionStatus || isConfig" class="flex">
      <ScrollContainer class="w-400px!">
        <div class="max-h-[calc(100vh_-_168px)] w-400px lt-lg:w-300px lt-md:w-200px">
          <!-- 项目成员分组 -->
          <div class="bg-FO-Container-Fill1 h-fit rounded-md p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center font-bold">
                项目成员分组
              </div>
              <Button
                class="custom-rounded-btn"
                borderColor="black"
                size="small"
                noIcon
                @click="goToGroupConfig()"
              >
                前往分组配置
                <Icon icon="carbon:chevron-right" size="16" class="-mx-1" />
              </Button>
            </div>
            <div class="c-FO-Content-Text2 my-3 text-xs">
              该分组对整个项目生效，可用于配置当前分支的提交权限
            </div>
            <div :class="`${prefixCls}__tab`">
              <div
                v-for="item in projectGroupList"
                :key="item.ID"
                :class="`${prefixCls}__tab-item`"
                :active="!isSpecialUser && item.ID === activeGroup?.ID"
                @click="handleGroupChange(item.ID!)"
              >
                <div class="w-250px flex items-center">
                  <Icon icon="icon-park-solid:peoples" />
                  <ATypographyText
                    class="ml-2"
                    :ellipsis="{ tooltip: true }"
                    :content="item.name"
                  />
                </div>
              </div>
            </div>
          </div>
          <!-- 特殊干员 -->
          <div class="bg-FO-Container-Fill1 mt-4 h-fit rounded-md p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center font-bold">
                特殊干员
              </div>
              <Button
                class="custom-rounded-btn"
                borderColor="black"
                size="small"
                noIcon
                @click="goToGroupConfig()"
              >
                前往分组配置
                <Icon icon="carbon:chevron-right" size="16" class="-mx-1" />
              </Button>
            </div>
            <div class="c-FO-Content-Text2 my-3 text-xs">
              单独为特殊干员配置权限
            </div>
            <div :class="`${prefixCls}__tab`">
              <div
                v-for="item in specialUserList"
                :key="item.ID"
                :class="`${prefixCls}__tab-item`"
                :active="isSpecialUser && item.ID === activeSpecialUser?.ID"
                @click="handleGroupChange(item.ID!, true)"
              >
                <div class="w-250px flex items-center">
                  <Icon icon="icon-park-solid:people" />
                  <ATypographyText
                    class="ml-2"
                    :ellipsis="{ tooltip: true }"
                    :content="formatNickName(item.user)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </ScrollContainer>
      <ScrollContainer class="flex-1">
        <div class="ml-4 max-h-[calc(100vh_-_168px)]">
          <PermissionContent
            :isSpecialUser="isSpecialUser"
            :activeGroup="activeGroup"
            :activeSpecialUser="activeSpecialUser"
            @success="handleSuccess"
          />
        </div>
      </ScrollContainer>
    </div>
    <div
      v-if="!curStream?.permissionStatus && !isConfig && !isCloneConfig"
      class="bg-FO-Container-Fill1 m-4 rounded-md p-4"
    >
      <div class="my-10 w-full flex flex-col items-center justify-center">
        <div class="c-FO-Content-Text2 text-lg">
          该分支未配置提交权限
        </div>
        <div class="mt-6">
          <Button type="primary" @click="handleConfig">
            配置
          </Button>
          <Button class="ml-3" type="primary" @click="isCloneConfig = true">
            克隆其他分支的配置
          </Button>
        </div>
      </div>
    </div>
    <div v-if="isCloneConfig" class="bg-FO-Container-Fill1 m-4 rounded-md p-4">
      <div class="my-10 flex items-center justify-center">
        <Icon icon="devGuard-submit-permission|svg" :size="80" :style="{ color: '#0067C7' }" />
        <div class="ml-8">
          <div class="c-FO-Content-Text1">
            从已存在分支中克隆提交权限配置:
          </div>
          <div class="w-300px flex items-center">
            <div class="w-80px">
              <div class="text-sm font-bold">
                分支：
              </div>
              <div class="c-FO-Content-Text2 text-xs">
                克隆分支
              </div>
            </div>
            <a-select
              v-model:value="copyFromStreamID"
              class="w-full !my-5"
              showSearch
              optionFilterProp="description"
              :options="copyStreamList"
              placeholder="请选择源分支"
              :fieldNames="{
                label: 'description',
                value: 'ID',
              }"
              allowClear
            />
          </div>
          <div class="mt-4">
            <Button type="primary" :loading="isCloneLoading" @click="copyPermission">
              克隆
            </Button>
            <Button class="ml-4" @click="isCloneConfig = false">
              取消
            </Button>
          </div>
        </div>
      </div>
    </div>
    <WhiteListModal @register="registerModal" />
    <DeleteModal @register="registerDeleteModal" @success="init" />
  </PageWrapper>
</template>

<script lang="ts" setup name="P4Permission">
import { TypographyText as ATypographyText, Button } from 'ant-design-vue';
import { computed, onMounted, ref, unref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useP4DepotStream } from '../hook';
import PermissionContent from './Content.vue';
import DeleteModal from './DeleteModal.vue';
import WhiteListModal from './WhiteListModal.vue';
import { ScrollContainer } from '/@/components/Container';
import type {
  P4GroupListItem,
  P4SpecialUserListItem,
  StreamsListItem,
} from '/@/api/page/model/p4Model';
import type { ManagementToolListItem } from '/@/api/page/model/systemModel';
import {
  copyP4StreamPermission,
  getP4GroupListByPage,
  getP4SpecialUserListByPage,
  updateP4GroupPermissionList,
  updateP4SpecialUserPermissionList,
} from '/@/api/page/p4';
import { getProjectManagementToolListByPage } from '/@/api/page/system';
import Icon from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import { PageWrapper } from '/@/components/Page';
import { useTimeoutFn } from '/@/hooks/core/useTimeout';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGo } from '/@/hooks/web/usePage';
import { useTabs } from '/@/hooks/web/useTabs';
import { useAppStore } from '/@/store/modules/app';
import { useP4StoreWithOut } from '/@/store/modules/p4';
import { useUserStoreWithOut } from '/@/store/modules/user';

const { prefixCls } = useDesign('p4-permission');
const { currentRoute } = useRouter();
const [registerModal, { openModal }] = useModal();
const [registerDeleteModal, { openModal: openDeleteModal }] = useModal();
const { createMessage } = useMessage();
const { refreshPage } = useTabs();
const userStore = useUserStoreWithOut();
const depotID = Number(unref(currentRoute).params.id);

const curStream = ref<StreamsListItem>();
const streamID = Number(unref(currentRoute).params.stream_id);
const serverID = Number(unref(currentRoute).query.sID);
const streamName = ref<string>('');

const projectGroupList = ref<P4GroupListItem[]>([]);
const specialUserList = ref<P4SpecialUserListItem[]>([]);
const isSpecialUser = ref<boolean>(false);
const activeGroup = ref<P4GroupListItem>();
const activeSpecialUser = ref<P4SpecialUserListItem>();
const p4Store = useP4StoreWithOut();
const go = useGo();
const isConfig = ref<boolean>(false);
const isCloneConfig = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const isCloneLoading = ref<boolean>(false);
const copyFromStreamID = ref<number>();
const appStore = useAppStore();
// 项目管理工具信息
const managementToolInfo = ref<ManagementToolListItem>();

async function getManageMentToolList() {
  if (!userStore.getProjectId) {
    return;
  }

  const { list } = await getProjectManagementToolListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 1,
  });

  managementToolInfo.value = list?.[0] || undefined;
}

const { allStreamList, getAllStreamList } = useP4DepotStream();

// 复制权限
async function copyPermission() {
  if (unref(copyFromStreamID)) {
    isCloneLoading.value = true;
    await copyP4StreamPermission(userStore.getProjectId, depotID, {
      fromStreamID: unref(copyFromStreamID) as number,
      toStreamID: streamID,
    });
    copyFromStreamID.value = undefined;
    p4Store.setIsAfterCopy(true);
    createMessage.success('正在克隆，请稍候...');
    useTimeoutFn(() => refreshPage(), 2000);
  } else {
    createMessage.warn('请选择源分支');
  }
}

// 复制分支权限的分支下拉框, 过滤掉没有权限的分支和虚拟分支
const copyStreamList = computed(() => allStreamList.value?.filter((e) => e.permissionStatus && e.streamType !== 4));

function handleDelete() {
  openDeleteModal(true, {});
}

// 打开提交白名单弹窗
function handleSubmitWhiteList() {
  openModal(true, {});
}

// 跳转到分组配置
function goToGroupConfig() {
  go({
    name: 'P4MemberManagement',
  });
}

// 获取分支列表
async function getStreamList() {
  await getAllStreamList(depotID);
  curStream.value = allStreamList.value?.find((e) => e.ID === streamID);

  if (curStream.value) {
    p4Store.setCurStream(curStream.value);
    streamName.value = curStream.value?.description || curStream.value?.path || '';
  }
}

// 获取分组列表
async function getProjectGroupList() {
  if (!userStore.getProjectId) {
    return;
  }

  const { list } = await getP4GroupListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 999,
    serverID,
  });

  projectGroupList.value = list || [];

  if (!isSpecialUser.value) {
    activeGroup.value = list?.[0];
  }
}

// 获取特殊干员列表
async function getSpecialUserList() {
  if (!userStore.getProjectId) {
    return;
  }

  const { list } = await getP4SpecialUserListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 999,
  });

  specialUserList.value = list || [];
}

// 切换分组
function handleGroupChange(ID: number, specialUser = false) {
  if (specialUser) {
    activeSpecialUser.value = specialUserList.value.find((e) => e.ID === ID);
  } else {
    activeGroup.value = projectGroupList.value.find((e) => e.ID === ID);
  }

  isSpecialUser.value = specialUser;
}

// 页面左侧点击返回链接时的操作
function goBack() {
  go({ name: 'P4Depots' });
}

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      goBack();
    }
  },
);

async function init() {
  if (!userStore.getProjectId) {
    return;
  }

  appStore.setPageLoadingAction(true);
  isLoading.value = true;
  isConfig.value = false;
  await getStreamList();
  await getProjectGroupList();
  await getSpecialUserList();
  isLoading.value = false;
  await getManageMentToolList();
  appStore.setPageLoadingAction(false);
}

async function handleConfig() {
  isConfig.value = true;
  if (!isSpecialUser.value && activeGroup.value?.ID) {
    await updateP4GroupPermissionList(
      userStore.getProjectId,
      activeGroup.value.ID,
      { streamID, list: [] },
    );
  } else if (activeSpecialUser.value?.ID) {
    await updateP4SpecialUserPermissionList(
      userStore.getProjectId,
      activeSpecialUser.value.ID,
      { streamID, list: [] },
    );
  }
  return getStreamList();
}

function handleSuccess() {
  getStreamList();
  isConfig.value = false;
  isCloneConfig.value = false;
}

onMounted(async () => {
  await init();
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-p4-permission';
.@{prefix-cls} {
  &__tab {
    margin: 8px 0;

    &-item {
      padding: 8px;
      margin: 8px 0;
      border: 1px solid transparent;
      border-radius: 6px;
      display: flex;
      justify-content: space-between;
      cursor: pointer;

      &:hover {
        border-color: @FO-Container-Stroke1;
      }

      &[active='true'] {
        border-color: @FO-Brand-Primary-Default;
      }
    }
  }
}
</style>
