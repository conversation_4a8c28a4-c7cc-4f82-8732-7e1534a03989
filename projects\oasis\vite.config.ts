import { defineViteConfigProjectVue, getPreviewBasePath } from '@hg-tech/configs';
import path from 'node:path';
import UnoCSS from 'unocss/vite';
import * as process from 'node:process';
import { fileURLToPath } from 'node:url';
import svgLoader from 'vite-svg-loader';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import { viteStaticCopy } from 'vite-plugin-static-copy';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineViteConfigProjectVue(({ mode }) => {
  return {
    base: getPreviewBasePath({
      mode,
      projectName: 'forgeon',
    }),
    resolve: {
      alias: [
        // /@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: `${path.join(__dirname, 'src')}/`,
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: `${path.join(__dirname, 'types')}/`,
        },
      ],
    },
    plugins: [
      UnoCSS(),
      svgLoader(),
      createSvgIconsPlugin({
        iconDirs: [path.join(__dirname, 'src/assets/icons')],
        svgoOptions: {
          plugins: [{ name: 'removeUselessStrokeAndFill', active: false }],
        },
        symbolId: 'icon-[dir]-[name]',
      }),
      viteStaticCopy({
        targets: [
          {
            src: 'node_modules/vditor/dist',
            dest: 'resource/vditor',
          },
        ],
      }),
    ],
    build: {
      target: 'es2015',
      cssTarget: 'chrome80',
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            // @deprecated 后续不再使用
            hack: `true;
      @import (reference) "@hg-tech/oasis-common/styles/index.less";
      @import (reference) "@hg-tech/forgeon-style/vars.less"
    `,
          },
          javascriptEnabled: true,
        },
      },
    },
    server: {
      host: true,
      port: 3100,
      proxy: {
        '/api': {
          ws: true,
          target: 'https://t-tech.int.hypergryph.com/api',
          // target: 'https://t2-tech.int.hypergryph.com/api',
          secure: false,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
        '/uploads': {
          ws: true,
          target: 'https://t-tech.int.hypergryph.com/api/uploads',
          // target: 'https://t2-tech.int.hypergryph.com/api/uploads',
          secure: false,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/uploads/, ''),
        },
      },
    },
  };
}, {
  analyze: process.env.REPORT === 'true',
});
