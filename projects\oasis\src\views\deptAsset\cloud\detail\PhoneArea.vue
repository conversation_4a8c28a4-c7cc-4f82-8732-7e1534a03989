<template>
  <div class="phone-area h-full flex flex-col">
    <div class="h-full min-h-0 w-full flex flex-1">
      <div class="relative flex flex-1 items-center justify-center overflow-hidden rounded-20px p-2">
        <input
          ref="inputBox" v-model="inputValue" class="absolute z-[-1] h-0 w-0 caret-transparent opacity-0"
          type="text" :style="inputBoxStyle" @input="changeInputHandle" @keyup.delete="deleteInputHandle"
          @keyup.enter="enterInputHandle"
        >
        <template v-if="cloudDeviceStore.isAndroid">
          <video
            v-show="websocketStore.screenMode === 'Scrcpy'" ref="scrcpyVideoRef" class="phone-area-video"
            :class="websocketStore.directionStatus === 0 || websocketStore.directionStatus === 180 ? '!w-auto' : '!w-full !h-auto'"
            autoplay muted @mouseup="mouseup" @mousemove="mousemove" @mousedown="mousedown" @mouseleave="mouseleave"
          />
          <canvas
            v-show="websocketStore.screenMode !== 'Scrcpy'" ref="canvasRef" class="phone-area-video"
            :class="websocketStore.directionStatus === 0 || websocketStore.directionStatus === 180 ? '!w-auto' : '!w-full !h-auto'"
            @mouseup="mouseup" @mousemove="mousemove" @mousedown="mousedown" @mouseleave="mouseleave"
          />
        </template>
        <canvas
          v-else ref="iosCapRef" class="phone-area-video"
          :class="websocketStore.directionStatus === 0 || websocketStore.directionStatus === 180 ? '!w-auto' : '!w-full !h-auto'"
          @mouseup="iosMouseup" @mousedown="iosMousedown" @mouseleave="iosMouseleave"
        />
        <div
          v-if="websocketStore.loading"
          class="absolute left-0 top-0 h-full w-full flex items-center justify-center bg-FO-Container-Fill1 p-2"
        >
          <div class="h-full w-full flex flex-col items-center justify-center gap-2 rounded-20px bg-[#303133]">
            <Icon :icon="LoadingOne" :size="64" class="animate-spin c-FO-Content-Components1" />
            <span class="c-FO-Content-Components1">准备图像中...</span>
          </div>
        </div>
        <audio ref="audioPlayerRef" hidden />
      </div>

      <!-- 右侧控制按钮 -->
      <div class="h-full flex flex-col items-center justify-center gap-3 px-2">
        <Button
          v-tippy="{ content: '截图', placement: 'right' }" shape="circle" class="control-btn"
          @click="() => handleScreenshot()"
        >
          <Icon :icon="ScreenshotTwo" :size="16" />
        </Button>
        <Button
          v-if="cloudDeviceStore.isAndroid" v-tippy="{ content: '物理查找', placement: 'right' }" shape="circle"
          class="control-btn" @click="() => searchDevice()"
        >
          <Icon :icon="Local" :size="16" />
        </Button>
        <Button
          v-tippy="{ content: '锁定/解锁屏幕', placement: 'right' }" shape="circle" class="control-btn"
          @click="() => handleLockScreen()"
        >
          <Icon :icon="Power" :size="16" />
        </Button>
      </div>
    </div>
    <!-- 底部导航区域 -->
    <div class="flex justify-center px-2 pb-2">
      <div v-if="cloudDeviceStore.isAndroid" class="grid grid-cols-3 w-full gap-1px">
        <Button class="nav-btn" @click="() => pressKey('187')">
          <Icon :icon="Copy" :size="18" />
        </Button>
        <Button class="nav-btn" @click="() => pressKey('3')">
          <Icon :icon="Home" :size="18" />
        </Button>
        <Button class="nav-btn" @click="() => pressKey('4')">
          <Icon :icon="ArrowLeft" :size="18" />
        </Button>
      </div>
      <Button v-else class="nav-btn w-full" @click="() => iosPressKey('home')">
        <Icon :icon="Home" :size="18" />
      </Button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Button, message } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import ArrowLeft from '@iconify-icons/icon-park-outline/arrow-left';
import Home from '@iconify-icons/icon-park-outline/home';
import Copy from '@iconify-icons/icon-park-outline/copy';
import ScreenshotTwo from '@iconify-icons/icon-park-outline/screenshot-two';
import Local from '@iconify-icons/icon-park-outline/local';
import Power from '@iconify-icons/icon-park-outline/power';
import LoadingOne from '@iconify-icons/icon-park-outline/loading-one';
import { useCloudDeviceStore, useCloudDeviceWebsocketStore } from '../stores';
import { useScreenControl } from '../composables/useScreenControl';
import { useScreenshot } from '../composables/useScreenshot';
import { storeToRefs } from 'pinia';

const websocketStore = useCloudDeviceWebsocketStore();
const cloudDeviceStore = useCloudDeviceStore();

const {
  scrcpyVideoRef,
  iosCapRef,
  canvasRef,
  audioPlayerRef,
} = storeToRefs(websocketStore);

const {
  inputValue,
  inputBoxStyle,
  inputBox,
  mousedown,
  mousemove,
  mouseup,
  mouseleave,
  changeInputHandle,
  deleteInputHandle,
  enterInputHandle,
  pressKey,
  iosPressKey,
  searchDevice,
  iosMouseup,
  iosMouseleave,
  iosMousedown,
  handleLockScreen,
} = useScreenControl();

const { quickCap } = useScreenshot();

function handleScreenshot() {
  quickCap();
  message.success('已保存到截图');
}
</script>

<style lang="less" scoped>
.phone-area-video {
  @apply inline-block h-auto max-h-full b-3 b-[#303133] rounded-20px b-solid bg-[#303133] w-auto;
  cursor:
    url('/@/assets/images/mtl/pointer.png') 12 12,
    crosshair;
}
.nav-btn {
  @apply rounded-0 b-none flex items-center justify-center !c-FO-Content-Text1;
  background-color: @FO-Container-Fill2 !important;
  &:hover {
    @apply c-FO-Brand-Primary-Default;
    background-color: @FO-Container-Fill3;
  }
}

.control-btn {
  @apply b-FO-Container-Stroke2 hover:b-FO-Brand-Primary-Default;
}
</style>
