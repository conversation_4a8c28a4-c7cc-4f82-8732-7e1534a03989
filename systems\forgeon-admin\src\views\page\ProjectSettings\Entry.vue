<template>
  <PageContainer class="h-full flex flex-col">
    <div class="mb-[12px] flex items-center justify-between">
      <div class="flex items-center gap-12px">
        <div class="font-bold">
          项目列表
        </div>
      </div>
      <div class="flex items-center gap-12px">
        <Input v-model:value="searchKeyword" allowClear placeholder="搜索项目" class="w-240px">
          <template #prefix>
            <SearchOutlined class="c-gray" />
          </template>
        </Input>
        <Button type="primary" @click="handleAddProject">
          创建项目
        </Button>
      </div>
    </div>

    <div v-if="renderTableData.length === 0 && !loading && searchKeyword" class="py-20 text-center">
      <div class="text-lg text-gray-400">
        暂无结果
      </div>
    </div>

    <div v-else class="h-full flex flex-1 flex-col overflow-hidden">
      <div class="h-full flex-1 overflow-hidden">
        <BasicVxeTable ref="tableRef" :options="gridOptions" />
      </div>
    </div>
    <FormModalHolder />
  </PageContainer>
</template>

<script setup lang="tsx">
import { Button, Input, message } from 'ant-design-vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import type { ArrayWrappedType } from '@hg-tech/utils';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import { computed, ref, watch } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { type VxeGridInstance, type VxeGridProps, BasicVxeTable } from '@hg-tech/oasis-common';
import PageContainer from '../../../components/Framework/PageContainer.vue';
import ProjectFormModal from './ProjectFormModal.vue';
import FillProjectIcon from '../../../assets/icons/fill-project.svg?component';
import dayjs from 'dayjs';
import { forgeonAdminApi } from '../../../api/index.ts';

const [FormModalHolder, show] = useModalShow(ProjectFormModal);

const searchKeyword = ref<string>('');

const { data, loading, execute } = useLatestPromise((params: { page: number; pageSize: number; filter?: string }, options: Record<string, never>) =>
  forgeonAdminApi.api.configServiceListConfigProject(params, options),
);
const renderTableData = computed(() => data.value?.data?.data?.list ?? []);

// 获取项目列表
function refreshTableData() {
  return execute({
    page: 1,
    pageSize: 99,
    filter: searchKeyword.value || undefined,
  }, {});
}

// 初始化加载数据
refreshTableData();

const debouncedSearch = useDebounceFn(() => {
  refreshTableData();
}, 300);

watch(searchKeyword, debouncedSearch);

const tableRef = ref<VxeGridInstance>();

const tableColumns = computed<VxeGridProps['columns']>(() => [
  { type: null, width: 40, dragSort: true },
  {
    field: 'name',
    title: '项目名称',
    slots: {
      default({ row }) {
        return (
          <div class="flex items-center gap-12px">
            {row.iconThumbnail
              ? (
                <img
                  alt={row.name}
                  class="h-8 w-8 rounded object-cover"
                  src={row.iconThumbnail}
                />
              )
              : (
                <div class="h-8 w-8 flex items-center justify-center rounded bg-gray-100">
                  <FillProjectIcon class="text-16px text-gray-400" />
                </div>
              )}
            <span>{row.name}</span>
          </div>
        );
      },
    },
  },
  {
    field: 'alias',
    title: '代号',
    width: 200,
  },
  {
    field: 'createdAt',
    title: '创建时间',
    width: 180,
    slots: {
      default({ row }) {
        return row.createdAt ? dayjs(row.createdAt).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
  },
  {
    field: 'actions',
    title: '操作',
    align: 'center',
    width: 180,
    slots: {
      default({ row }) {
        return (
          <div class="flex items-center justify-center gap-8px">
            <Button onClick={() => handleEditProject(row)} type="link">编辑</Button>
          </div>
        );
      },
    },
  },
]);

// vxetable 配置
const gridOptions = computed(() => ({
  rowConfig: {
    keyField: 'id',
    drag: true,
    isHover: true,
  },
  columnConfig: {
    resizable: false,
  },
  rowDragConfig: {
    isPeerDrag: true,
    async dragEndMethod({ newRow, dragRow }) {
      if (!newRow || !dragRow || newRow.id === dragRow.id) {
        return false;
      }

      try {
        // 重新计算排序
        const newOrder = [...renderTableData.value];
        const dragIndex = newOrder.findIndex((item) => item.id === dragRow.id);
        const targetIndex = newOrder.findIndex((item) => item.id === newRow.id);

        if (dragIndex === -1 || targetIndex === -1) {
          return false;
        }

        // 移除拖拽项
        const [movedItem] = newOrder.splice(dragIndex, 1);

        // 插入到新位置
        newOrder.splice(targetIndex, 0, movedItem);

        await forgeonAdminApi.api.configServiceSortConfigProject({}, { ids: newOrder.map((item) => String(item.id!)) });
        message.success('排序已保存');
        await refreshTableData();
        return true;
      } catch (error) {
        console.error('排序失败:', error);
        message.error('排序失败，请重试');
        return false;
      }
    },
  },
  columns: tableColumns.value,
  data: renderTableData.value,
  loading: loading.value,
}) as VxeGridProps);

async function handleAddProject() {
  await show({
    title: '创建项目',
    sentReq(formValue) {
      return forgeonAdminApi.api.configServiceCreateConfigProject({}, formValue);
    },
  });
  message.success('创建成功');
  return refreshTableData();
}

async function handleEditProject(row: ArrayWrappedType<typeof renderTableData.value>) {
  await show({
    title: '编辑项目',
    iconPreviewMap: row.icon ? { [row.icon]: row.iconPreview! } : {},
    initData: {
      name: row.name,
      alias: row.alias,
      icon: row.icon,
    },
    isEdit: true,
    sentReq(formValue) {
      return forgeonAdminApi.api.configServiceUpdateConfigProject({ id: String(row.id) }, formValue);
    },
  });
  message.success('编辑成功');
  return refreshTableData();
}
</script>
