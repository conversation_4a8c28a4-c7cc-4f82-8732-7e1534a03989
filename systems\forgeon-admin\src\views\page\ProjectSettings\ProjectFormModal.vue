<template>
  <Modal
    width="680px"
    :open="show"
    :title="title"
    :maskClosable="false"
    :destroyOnClose="true"
    :centered="true"
    :afterClose="modalDestroy"
    @ok="handleConfirm"
    @cancel="() => modalCancel()"
  >
    <Form :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
      <FormItem label="图标" v-bind="validateInfos.icon">
        <Uploader
          v-model:url="formValue.icon"
          accept="image/*"
          :previewMap="iconPreviewMap"
          :maxSize="1024"
          class="flex-shrink-0"
        />
      </FormItem>
      <FormItem label="项目名称" v-bind="validateInfos.name">
        <Input
          v-model:value="formValue.name"
          placeholder="请输入项目名称"
          :maxlength="30"
          showCount
        />
      </FormItem>
      <FormItem label="代号" v-bind="validateInfos.alias">
        <Input
          v-model:value="formValue.alias"
          placeholder="请输入代号"
          :maxlength="30"
          :disabled="isEdit"
          showCount
        />
      </FormItem>
    </Form>
  </Modal>
</template>

<script setup lang="tsx">
import type { RuleObject } from 'ant-design-vue/es/form/interface';
import { ref } from 'vue';
import { Form, FormItem, Input, Modal } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import type { ProjectFormData, ProjectItem } from './FormData.ts';
import Uploader from '../../../components/Uploader.vue';

const props = defineProps<ModalBaseProps & {
  title?: string;
  iconPreviewMap?: { [url: string]: string };
  initData?: ProjectItem;
  isEdit?: boolean;
  sentReq: (formValue: ProjectFormData) => Promise<unknown>;
}>();

const formValue = ref<ProjectFormData>({
  name: props.initData?.name,
  alias: props.initData?.alias,
  icon: props.initData?.icon || undefined,
});

const formRule = ref<Record<keyof ProjectFormData, RuleObject[]>>({
  name: [
    { required: true, message: '请输入项目名称' },
    { max: 30, message: '项目名称不能超过30个字符' },
  ],
  alias: [
    { required: true, message: '请输入代号' },
    { max: 30, message: '代号不能超过30个字符' },
  ],
  icon: [],
});

const { validate, validateInfos } = Form.useForm(formValue, formRule);

async function handleConfirm() {
  try {
    const formRes = await validate();
    await props.sentReq(formRes);
    await props.modalConfirm();
  } catch (error: any) {
    console.error('创建项目失败:', error);
  }
}
</script>
