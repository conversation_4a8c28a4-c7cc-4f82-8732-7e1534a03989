<template>
  <Modal
    :width="600"
    :open="show"
    :maskClosable="false"
    destroyOnClose
    centered
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="FO-Font-B16">
          <span>关注配置</span>
        </span>
      </div>
    </template>
    <div class="concern-modal m-[20px]">
      <BorderBox class="mx-[24px]" label="被关注人" subLabel="被配关注的干员提交时触发该审查组的review。reviewer请在审查配置中添加">
        <Form :labelCol="labelCol" :wrapperCol="wrapperCol" labelAlign="left">
          <FormItem label="添加干员" v-bind="validateInfos.concernMemberIDs">
            <UserSelect v-model:value="modelRef.concernMemberIDs " isMultiple placeholder="请选择干员" />
          </FormItem>
          <FormItem label="添加干员组" v-bind="validateInfos.concernMemberGroups">
            <TreeSelect
              v-model:value="modelRef.concernMemberGroups"
              treeCheckable
              showCheckedStrategy="SHOW_PARENT"
              :treeData="groupList"
              showSearch
              allowClear
              showArrow
              mode="multiple"
              placeholder="请选择干员组"
            >
              <template #tagRender="{ label, closable, onClose }">
                <GroupUsersPopover :groupName="label" :serverID="serverID">
                  <Tag :closable="closable" class="!text-sm" @close="onClose">
                    {{ label }}
                  </Tag>
                </GroupUsersPopover>
              </template>
            </TreeSelect>
          </FormItem>

          <FormItem v-bind="validateInfos.mustReviewConcernSubmitOption" class="mb-0">
            <Checkbox v-model:checked="modelRef.mustReviewConcernSubmitOption">
              特别关注人提交任何路径都触发该review配置
            </Checkbox>
          </FormItem>
        </Form>
      </borderbox>
    </div>
    <template #footer>
      <div class="mt flex justify-center">
        <a-button @click="modalDestroy()">
          取消
        </a-button>
        <a-button type="primary" class="ml-2" @click="handleConfirm">
          确定
        </a-button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { type TreeSelectProps, Checkbox, Form, FormItem, Modal, Tag, TreeSelect } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import type { NullableBasicResult } from '/@/api/model/baseModel';
import { BorderBox } from '../../../../components/Form';
import { computed, reactive, watchEffect } from 'vue';
import GroupUsersPopover from '../../../../components/GroupUsersPopover';
import { useRouter } from 'vue-router';
import { UserSelect } from '/@/components/Form';
import type { SwarmGroupItemInfo, SwarmReviewGroupsConcernParams } from '/@/api/page/model/swarmModel';

const props = defineProps< ModalBaseProps<{ updatedItem?: NullableBasicResult }> & {
  concernGroup: SwarmGroupItemInfo['concernGroup'];
  groupList: TreeSelectProps['treeData'];
  sentReq: (formValue: SwarmReviewGroupsConcernParams) => Promise<NullableBasicResult>;
}>();

const { currentRoute } = useRouter();
const serverID = computed(() => Number(currentRoute.value.query.sID));
const useForm = Form.useForm;
const modelRef = reactive<SwarmReviewGroupsConcernParams>({
  concernMemberIDs: [],
  concernMemberGroups: [],

  mustReviewConcernSubmitOption: false,
});
const labelCol = { span: 5 };
const wrapperCol = { span: 19 };
const { validateInfos } = useForm(modelRef);
watchEffect(() => {
  modelRef.concernMemberIDs = props.concernGroup?.manualUsers?.map((item) => item.ID).filter((id): id is number => id !== undefined) || [];
  modelRef.concernMemberGroups = props.concernGroup?.p4Groups ?? [];

  modelRef.mustReviewConcernSubmitOption = props.concernGroup?.mustReviewConcernSubmitOption ?? false;
});
async function handleConfirm() {
  modelRef.mustReviewConcernSubmitOption = !!modelRef.mustReviewConcernSubmitOption;
  const updatedItem = await props.sentReq(modelRef);
  return props.modalConfirm({ updatedItem });
}
</script>
