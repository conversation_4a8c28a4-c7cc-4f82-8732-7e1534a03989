import type { BasicAddResult, NullableBasicResult } from '/@/api/model/baseModel';
import type {
  CopyP4StreamConcernParams,
  CopyP4StreamSwarmParams,
  GroupLockListItem,
  P4StreamLockConfigReviewerGetResultModel,
  P4StreamLockConfigReviewerItem,
  P4StreamLockConfigReviewerParams,
  ReviewUrgentListGetResultModel,
  ReviewUrgentListItem,
  ReviewUrgentPageParams,
  SwarmGroupItemInfo,
  SwarmGroupListItem,
  SwarmReviewGroupsConcernParams,
  SwarmReviewMessagesListGetResultModel,
  SwarmReviewMessagesListItem,
  SwarmReviewMessagesPageParams,
  SwarmReviewProjectsListGetResultModel,
  SwarmReviewProjectsListItem,
  SwarmReviewProjectsPageParams,
  SwarmWorkflowGlobalsGetResultModel,
  SwarmWorkflowGlobalsItem,
} from '/@/api/page/model/swarmModel';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  Projects = '/api/v1/projects',
  SwarmWorkflows = '/api/v1/review_workflows',
  SwarmProjects = '/review_projects',
  SwarmMessages = '/review_messages',
  SwarmGroups = '/review_groups',
  SwarmDefaultAuditors = '/reviewer_defaults',
  SwarmJointAuditors = '/reviewer_moderators',
  SwarmWorkflowGlobals = '/workflow/globals',
}

/**
 * 获取Swarm审查项目列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getSwarmReviewProjectsListByPage(projectID: number, params?: SwarmReviewProjectsPageParams) {
  return defHttp.get<SwarmReviewProjectsListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.SwarmProjects}`,
    params,
  });
}

/**
 * 新增Swarm审查项目
 * @param projectID 项目id
 * @param data Swarm审查项目数据
 */
export function addSwarmReviewProject(projectID: number, data: SwarmReviewProjectsListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.Projects}/${projectID}${Api.SwarmProjects}`, data });
}

/**
 * 编辑Swarm审查项目
 * @param projectID 项目id
 * @param data Swarm审查项目数据
 * @param editId Swarm审查项目id
 */
export function editSwarmReviewProject(projectID: number, data: SwarmReviewProjectsListItem, editId: number) {
  return defHttp.put<null>({
    url: `${Api.Projects}/${projectID}${Api.SwarmProjects}/${editId}`,
    data,
  });
}

/**
 * 删除Swarm审查和关注项目
 * @param projectID 项目id
 */
export function deleteSwarmReviewAndConcernProject(projectID: number, streamID: number) {
  return defHttp.delete<null>({ url: `${Api.Projects}/${projectID}/streams/${streamID}/swarm` });
}

/**
 * 新增Swarm审查组
 */
export function addSwarmGroup(projectID: number, data: {
  /**
   * 创建名称
   */
  name?: string;
  /**
   * 分支 ID
   */
  streamId?: number;
}) {
  return defHttp.post<BasicAddResult>({
    url: `/api/v1/projects/${projectID}/swarm_review_groups/create`,
    data,
  });
}

/**
 * 根据id获取Swarm审查组信息
 */
export function getSwarmGroupByID(projectID: number, swarmReviewGroupID: number) {
  return defHttp.get<SwarmGroupItemInfo>({
    url: `/api/v1/projects/${projectID}/swarm_review_groups/get?swarmReviewGroupID=${swarmReviewGroupID}`,
  });
}

/**
 * 获取Swarm审查组列表
 */
export function getSwarmGroupListByPage(projectID: number, streamID?: number) {
  return defHttp.get<Array<SwarmGroupItemInfo>>({
    url: `/api/v1/projects/${projectID}/swarm_review_groups/list?streamID=${streamID}`,
  });
}

/**
 * 编辑审批
 */
export function editGroupLockProject(projectID: number, data: {
  lockGroupID?: number;
  streamID?: number;
  checkOption?: /* 审批人 */'checker' | /* 群聊 */'chat';
  checkerIDs?: number[];
  chatID?: string;
  chatName?: string;
  timeoutConfig?: GroupLockListItem['timeoutConfig'];
}) {
  return defHttp.post<NullableBasicResult>(
    {
      url: `/api/v1/projects/${projectID}/swarm_review_groups/update/check`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 编辑审查
 */
export function editSwarmGroup(projectID: number, data: {
  swarmReviewGroupID?: number;
  streamID?: number;
  /**
   * 审查人->添加干员
   */
  reviewerIDs?: number[];
  /**
   * 审查人->添加干员组
   */
  reviewerGroups?: string[];
  /**
   * 将所有审查人作为Swarm跟进人
   */
  reviewerBindSwarmOption?: boolean;
  /**
   * 仅审查人可通过审查
   */
  onlyReviewerApproveOption?: boolean;
  /**
   * 添加干员
   */
  concernMemberIDs?: number[];
  /**
   * 添加干员组
   */
  concernMemberGroups?: string[];
  /**
   * 将审查组审查配置进队特别关注人生效
   */
  specialConcernEffectOption?: boolean;
  /**
   * 特别关注人提交任何路径都触发该审查组审查
   */
  mustReviewConcernSubmitOption?: boolean;
  timeoutConfig?: GroupLockListItem['timeoutConfig'];
}) {
  return defHttp.post<NullableBasicResult>({
    url: `/api/v1/projects/${projectID}/swarm_review_groups/update/review`,
    data,
  }, {
    successMessageMode: 'none',
  });
}

/**
 * 更新审查组名称
 */
export function editSwarmGroupName(projectID: number, data: {
  name?: string;
  streamID?: number;
  swarmReviewGroupID?: number;
}) {
  return defHttp.post<NullableBasicResult>(
    {
      url: `/api/v1/projects/${projectID}/swarm_review_groups/update/name`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 删除Swarm审查组
 */
export function deleteSwarmGroup(projectID: number, data: {
  swarmReviewGroupID?: number;
  streamID?: number;
}) {
  return defHttp.post<null>({
    url: `/api/v1/projects/${projectID}/swarm_review_groups/delete`,
    data,
  });
}

/**
 * 修改审查路径
 */
export function editAuditPath(projectID: number, data: {
  swarmReviewGroupID?: number;
  streamID?: number;
  groupID?: number;
  paths?: SwarmGroupListItem['reviewPaths'];
  customizePaths?: SwarmGroupListItem['customizeReviewPaths'];
}) {
  return defHttp.post<NullableBasicResult>({
    url: `/api/v1/projects/${projectID}/swarm_review_groups/path`,
    data,
  }, { successMessageMode: 'none' });
}

/**
 * 修改审查开关
 */
export function editAuditSwitch(projectID: number, data: {
  swarmReviewGroupID?: number;
  streamID?: number;
  reviewSwitch?: boolean;
}) {
  return defHttp.post<NullableBasicResult>(
    {
      url: `/api/v1/projects/${projectID}/swarm_review_groups/modify/reviewSwitch`,
      data,
    },
    { successMessageMode: 'none' },
  );
}

/**
 * 复制项目仓库分支Swarm配置
 * @param projectID 项目id
 * @param params 复制参数
 */
export function copyP4StreamSwarm(projectID: number, params: CopyP4StreamSwarmParams) {
  return defHttp.post<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}${Api.SwarmProjects}/copy`,
      params,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 复制分支锁配置
 * @param projectID 项目id
 * @param params 复制参数
 */
export function copyLockConfigs(projectID: number, params: CopyP4StreamConcernParams) {
  return defHttp.post<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}/lock_configs/copy`,
      params,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 复制审查配置（通知加急）
 * @param projectID 项目id
 * @param params 复制参数
 */
export function copyReviewConfigs(projectID: number, params: CopyP4StreamConcernParams) {
  return defHttp.post<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}/review/confs/copy`,
      params,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 获取Swarm审查消息列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getSwarmReviewMessagesListByPage(projectID: number, params?: SwarmReviewMessagesPageParams) {
  return defHttp.get<SwarmReviewMessagesListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.SwarmMessages}`,
    params,
  });
}

/**
 * 新增Swarm审查消息
 * @param projectID 项目id
 * @param data Swarm审查消息数据
 */
export function addSwarmReviewMessage(projectID: number, data: SwarmReviewMessagesListItem) {
  return defHttp.post<null>({ url: `${Api.Projects}/${projectID}${Api.SwarmMessages}`, data });
}

/**
 * 编辑Swarm审查消息
 * @param projectID 项目id
 * @param data Swarm审查消息数据
 * @param editId Swarm审查消息id
 */
export function editSwarmReviewMessage(projectID: number, data: SwarmReviewMessagesListItem, editId: number) {
  return defHttp.put<null>({
    url: `${Api.Projects}/${projectID}${Api.SwarmMessages}/${editId}`,
    data,
  });
}

/**
 * 获取Swarm全局工作流
 */
export function getSwarmWorkflowGlobals(projectID: number, serverID?: number) {
  return defHttp.get<SwarmWorkflowGlobalsGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.SwarmWorkflowGlobals}/project`,
    params: { serverID },
  });
}

/**
 * 新增Swarm全局工作流
 * @param projectID 项目id
 * @param data Swarm全局工作流数据
 */
export function addSwarmWorkflowGlobal(projectID: number, data: SwarmWorkflowGlobalsItem) {
  return defHttp.post<BasicAddResult>(
    {
      url: `${Api.Projects}/${projectID}${Api.SwarmWorkflowGlobals}`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 编辑Swarm全局工作流
 * @param projectID 项目id
 * @param data Swarm全局工作流数据
 * @param editId Swarm全局工作流id
 */
export function editSwarmWorkflowGlobal(projectID: number, data: SwarmWorkflowGlobalsItem, editId: number) {
  return defHttp.put<null>(
    {
      url: `${Api.Projects}/${projectID}${Api.SwarmWorkflowGlobals}/${editId}`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 获取指定人员可选值
 */
export function getGroupOptions(projectID: number, type: 'review' | 'check') {
  return defHttp.get<{
    additionalNotifierSpecialGroups?: { label: string;value: string }[];
    permissionProcess?: { label: string;value: string }[];
  }>(
    {
      url: `${Api.Projects}/${projectID}/review_groups/timeout/configs?type=${type}`,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 开启/关闭审核组锁
 */
export function switchGroupLockProject(projectID: number, editId?: number, data?: { switch: boolean }) {
  return defHttp.post<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}/lock_groups/${editId}/modifyLockSwitch`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 配置锁定通知群聊
 * @param projectID 项目id
 * @param params 审查团参数
 */
export function setP4StreamLockConfigChat(projectID: number, params: P4StreamLockConfigReviewerItem) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}/lock_configs/chat`,
    params,
  });
}

/**
 * 配置锁定审查团
 * @param projectID 项目id
 * @param params 审查团参数
 */
export function getP4StreamLockConfigReviewer(projectID: number, params: P4StreamLockConfigReviewerParams) {
  return defHttp.get<P4StreamLockConfigReviewerGetResultModel>({
    url: `${Api.Projects}/${projectID}/lock_configs`,
    params,
  });
}

/**
 * 获取通知加急列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getReviewUrgentListByPage(projectID: number, params?: ReviewUrgentPageParams) {
  return defHttp.get<ReviewUrgentListGetResultModel>({
    url: `${Api.Projects}/${projectID}/review/confs`,
    params,
  });
}

/**
 * 新增通知加急
 * @param projectID 项目id
 * @param data 通知加急数据
 */
export function addReviewUrgent(projectID: number, data: ReviewUrgentListItem) {
  return defHttp.post<BasicAddResult>(
    { url: `${Api.Projects}/${projectID}/review/confs`, data },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 编辑通知加急
 * @param projectID 项目id
 * @param data 通知加急数据
 * @param editId 通知加急id
 */
export function editReviewUrgent(projectID: number, data: ReviewUrgentListItem, editId: number) {
  return defHttp.put<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}/review/confs/${editId}`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}
/**
 * 关注配置
 * @param projectID 项目id
 * @param data
 */
export function updateSwarmReviewGroupsConcern(projectID: number, data: SwarmReviewGroupsConcernParams) {
  return defHttp.post<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}/swarm_review_groups/update/concern`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 特别关注开关
 * @param projectID 项目id
 * @param data
 * @param data.concernGroupID 特别关注组id
 * @param data.streamID 分支id
 * @param data.concernSwitch 开关
 * @param data.swarmReviewGroupID 审查组id
 */
export function updateSwarmReviewGroupsConcernSwitch(projectID: number, data: {
  concernGroupID?: number;
  streamID?: number;
  swarmReviewGroupID?: number;
  concernSwitch?: boolean;
}) {
  return defHttp.put<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}/swarm_review_groups/modify/concernSwitch`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}
