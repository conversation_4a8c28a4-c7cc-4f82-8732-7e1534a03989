<template>
  <div :class="prefixCls">
    <div class="my-15 w-full flex items-center justify-center">
      <div> <SvgIcon name="devGuard-clone-swarm" :size="80" /> </div>
      <div class="ml-6 w-400px">
        <div class="text-xl">
          从已存在分支中克隆审查配置:
        </div>
        <div class="mt-6 text-xl">
          <BasicForm @register="registerForm" />
        </div>
      </div>
    </div>
    <div class="w-full flex justify-center">
      <a-button type="primary" :loading="loading" @click="handleClone">
        克隆审查配置
      </a-button>
      <a-button class="ml-6" :loading="loading" @click="handleSkip">
        跳过
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { pick } from 'lodash-es';
import { type PropType, onMounted, ref } from 'vue';
import type { SwarmReviewProjectsListItem } from '/@/api/page/model/swarmModel';
import { addSwarmReviewProject, copyP4StreamSwarm } from '/@/api/page/swarm';
import { BasicForm, useForm } from '/@/components/Form/index';
import { SvgIcon } from '/@/components/Icon/index';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { cloneFormSchema } from '/@/views/versionControl/swarmSettings/swarmSettings.data';

defineOptions({
  name: 'CloneSwarm',
});

const props = defineProps({
  streamID: {
    type: Number,
    required: true,
  },
  auditList: {
    type: Array as PropType<SwarmReviewProjectsListItem[]>,
    default: () => [],
  },
  hasAudit: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['nextStep']);
const userStore = useUserStoreWithOut();

const { prefixCls } = useDesign('clone-swarm');
const loading = ref(false);

const [registerForm, { validate, updateSchema }] = useForm({
  labelWidth: 120,
  schemas: cloneFormSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 22 },
});

async function handleClone() {
  try {
    const values = await validate();

    loading.value = true;

    const originProject = props.auditList.find(
      (item) => item.ID === values.copyFromReviewProjectID,
    );
    const submitData = Object.assign(
      { streamID: props.streamID, name: values.name, description: values.name },
      pick(originProject, ['members', 'subGroups']),
    );

    if (!userStore.getProjectId) {
      return;
    }

    const { id } = await addSwarmReviewProject(userStore.getProjectId, submitData);

    if (id) {
      const res = await copyP4StreamSwarm(userStore.getProjectId, {
        fromReviewProjectID: values.copyFromReviewProjectID,
        toReviewProjectID: id,
      });
      emit('nextStep', { status: res?.code === 7 ? 2 : 1 });
    } else {
      emit('nextStep', { status: 2 });
    }
  } finally {
    loading.value = false;
  }
}

function handleSkip() {
  emit('nextStep', { status: 0 });
}

onMounted(async () => {
  if (props.hasAudit) {
    handleSkip();
  }

  await updateSchema({
    field: 'copyFromReviewProjectID',
    componentProps: {
      options: props.auditList?.filter((e) => e.stream?.streamType !== 4)?.map((e) => ({
        label: e.stream?.description || e.stream?.path,
        value: e.ID,
      })),
      optionFilterProp: 'label',
    },
  });
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-clone-swarm';
//  .@{prefix-cls} {
//  }
</style>
