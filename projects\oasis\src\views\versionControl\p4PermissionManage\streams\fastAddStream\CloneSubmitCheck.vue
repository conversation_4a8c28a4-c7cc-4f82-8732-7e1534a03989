<template>
  <div :class="prefixCls">
    <div class="my-20 w-full flex items-center justify-center">
      <div> <SvgIcon name="devGuard-clone-permission" :size="80" /> </div>
      <div class="ml-6">
        <div class="text-xl">
          从已存在分支中克隆提交检查配置:
        </div>
        <div class="mt-6 text-lg">
          分支:
          <a-select
            v-model:value="cloneStreamID"
            class="w-400px pl-3"
            placeholder="请选择源分支"
            showSearch
            optionFilterProp="description"
            :options="showStreamList"
            :fieldNames="{
              label: 'description',
              value: 'ID',
            }"
            allowClear
          />
        </div>
      </div>
    </div>
    <div class="w-full flex justify-center">
      <a-button type="primary" :loading="loading" @click="handleClone">
        克隆提交检查配置
      </a-button>
      <a-button class="ml-6" :loading="loading" @click="handleSkip">
        跳过
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { type PropType, computed, ref } from 'vue';
import type { StreamsListItem } from '/@/api/page/model/p4Model';
import { cloneSubmitConfig } from '/@/api/page/p4';
import { SvgIcon } from '/@/components/Icon/index';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStoreWithOut } from '/@/store/modules/user';

defineOptions({
  name: 'ClonePermission',
});

const props = defineProps({
  depotID: {
    type: Number,
    required: true,
  },
  streamID: {
    type: Number,
    required: true,
  },
  streamList: {
    type: Array as PropType<StreamsListItem[]>,
    default: () => [],
  },
});

const emit = defineEmits(['nextStep']);

const { prefixCls } = useDesign('clone-permission');

const { createMessage } = useMessage();
const userStore = useUserStoreWithOut();

const cloneStreamID = ref<number>();
const loading = ref<boolean>(false);

const showStreamList = computed(() => {
  return props.streamList.filter((item) => item.ID !== props.streamID);
});

async function handleClone() {
  if (!cloneStreamID.value) {
    createMessage.error('请选择源分支');

    return;
  }

  try {
    loading.value = true;

    if (!userStore.getProjectId) {
      return;
    }

    const res = await cloneSubmitConfig(userStore.getProjectId, {
      srcStreamID: cloneStreamID.value,
      dstStreamID: props.streamID,
    });

    if (res?.code === 7) {
      emit('nextStep', { status: 2 });
    } else {
      createMessage.success('克隆成功');
      emit('nextStep', { status: 1 });
    }
  } finally {
    loading.value = false;
  }
}

function handleSkip() {
  emit('nextStep', { status: 0 });
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-clone-permission';
//  .@{prefix-cls} {
//  }
</style>
