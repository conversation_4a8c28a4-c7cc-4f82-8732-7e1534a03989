import { useLatestPromise } from '@hg-tech/utils-vue';
import { type PermissionAppInfo, getPermissionRoleMe, PermissionRoleEnum } from '../api/app.ts';
import { type Ref, computed, watch } from 'vue';

export function useUserRole(appIdRef: Ref<PermissionAppInfo['id']>) {
  const { data: roleInfo, execute: fetchRoleInfo } = useLatestPromise(getPermissionRoleMe);

  watch(appIdRef, refreshRoleInfo, { immediate: true });

  const isAppAdmin = computed(() => checkRole(PermissionRoleEnum.AppAdmin));
  const isSystemAdmin = computed(() => checkRole(PermissionRoleEnum.SystemAdmin));
  const isProjectAdmin = computed(() => checkRole(PermissionRoleEnum.ProjectAdmin));
  const hasPermissionToEditApp = computed(() => isSystemAdmin.value || isAppAdmin.value);
  const hasPermissionToEditGroup = computed(() => hasPermissionToEditApp.value || isProjectAdmin.value);

  async function refreshRoleInfo() {
    if (appIdRef.value) {
      return fetchRoleInfo({ appId: appIdRef.value }, {});
    }
  }
  /**
   * 检查是否有某个角色
   */
  function checkRole(role: PermissionRoleEnum) {
    const permissionRole = roleInfo.value?.data?.data || [];
    return permissionRole.includes(role);
  }

  return {
    roleInfo,
    isAppAdmin,
    isSystemAdmin,
    isProjectAdmin,
    hasPermissionToEditApp,
    hasPermissionToEditGroup,
    refreshRoleInfo,
  };
}
