<template>
  <div class="game-package-doctor">
    <div v-if="doctorData" class="game-package-doctor__title">
      『<Icon :icon="getPlatformIconByVal(doctorData?.platform)" :size="26" />{{ doctorData?.game?.name }}({{ doctorData?.version?.version }})』的包体检测报告
    </div>
    <div class="flex">
      <!--  锚点 begin  -->
      <div class="game-package-doctor__anchor">
        <Anchor :offsetTop="200" :anchorList="anchorList" :titleOffsetTop="0" lineColor="#cdcdcd" />
      </div>
      <!--  锚点 end  -->
      <div class="game-package-doctor__content">
        <div :id="anchorList[0]" class="game-package-doctor__card">
          <div class="game-package-doctor__card-title">
            {{ anchorList[0] }}
          </div>
          <Proportion class="self-center" @getDoctorData="getDoctorData" />
        </div>
        <template v-if="doctorData">
          <div :id="anchorList[1]" class="game-package-doctor__card">
            <div class="game-package-doctor__card-title">
              {{ anchorList[1] }}
            </div>
            <RenderTable :columns="duplicateColumns" :tableData="doctorData?.doctor?.duplicate" />
          </div>
          <div :id="anchorList[2]" class="game-package-doctor__card">
            <div class="game-package-doctor__card-title">
              {{ anchorList[2] }}
            </div>
            <RenderTable :columns="modelMapColumns" :tableData="doctorData?.doctor?.uncompressed" />
          </div>
          <div :id="anchorList[3]" class="game-package-doctor__card">
            <div class="game-package-doctor__card-title">
              {{ anchorList[3] }}
            </div>
            <RenderTable :columns="modelMapColumns" :tableData="doctorData?.doctor?.big_size" />
          </div>
          <div :id="anchorList[4]" class="game-package-doctor__card">
            <div class="game-package-doctor__card-title">
              {{ anchorList[4] }}
            </div>
            <RenderTable :columns="uiPanelColumns" :tableData="doctorData?.doctor?.prefab?.ui_panel" />
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { anchorList } from './doctor.data';
import type { GamePackageDoctorListItem } from '/@/api/page/model/testModel';
import { Anchor } from '/@/components/Anchor';
import Icon from '/@/components/Icon';
import Proportion from '/@/views/test/gamePackage/doctor/proportion/index.vue';
import { platformOptions } from '/@/views/test/gamePackage/settings/settings.data';
import RenderTable from './RenderTable.vue';
import { duplicateColumns, modelMapColumns, uiPanelColumns } from './renderTableColumns.tsx';

const doctorData = ref<GamePackageDoctorListItem>();

function getDoctorData(doctor: GamePackageDoctorListItem) {
  doctorData.value = doctor;
}

function getPlatformIconByVal(val: number | undefined) {
  return platformOptions.find((e) => e.value === val)?.icon;
}
</script>

<style lang="less" scoped>
.game-package-doctor {
  position: relative;

  &__anchor {
    width: 8vw;
    margin: 100px auto 0;
  }

  &__content {
    width: 78vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &__title {
    font-size: 20px;
    font-weight: bold;
    margin: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__card {
    background-color: @FO-Container-Background;
    margin: 0 16px 16px;
    border-radius: 16px;
    padding: 0 16px 32px;
    width: 1000px;
    display: flex;
    flex-direction: column;

    &-title {
      width: 100%;
      font-size: 16px;
      font-weight: bold;
      padding: 16px;
      border-bottom: 1px solid;
      border-bottom-color: @FO-Container-Stroke1;
      position: relative;
      margin-bottom: 16px;

      &::before {
        position: absolute;
        content: '';
        width: 4px;
        height: 26px;
        background-color: @FO-Brand-Primary-Default;
        left: 4px;
        top: 16px;
      }
    }
  }
}
</style>
