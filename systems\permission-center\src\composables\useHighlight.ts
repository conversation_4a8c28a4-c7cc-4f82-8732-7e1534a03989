import { pinyin } from 'pinyin-pro';
import { toHighlightSegments } from '@hg-tech/utils';

/**
 * 搜索高亮相关的 composable
 */
export function useHighlight() {
  /**
   * 拼音高亮转换函数
   * 支持文字匹配和拼音匹配的高亮显示
   * @param v 输入字符
   * @returns [原始字符, 拼音全拼, 拼音首字母]
   */
  function highlightTransform(v: string): string[] {
    const py = pinyin(v, { toneType: 'none', type: 'string' });
    const initial = py[0] || v;
    return [v, py, initial]; // [原始字符, 拼音全拼, 拼音首字母]
  }

  /**
   * 获取高亮文本段落
   * @param text 要高亮的文本
   * @param searchKeyword 搜索关键词
   * @param useTransform 是否使用拼音转换（默认true）
   * @returns 高亮段落数组
   */
  function getHighlightSegments(text: string, searchKeyword: string, useTransform: boolean = true) {
    if (!searchKeyword) {
      return [{ text, highlight: false }];
    }

    const options = useTransform ? { transform: highlightTransform } : {};
    return toHighlightSegments(text, searchKeyword, options);
  }

  return {
    highlightTransform,
    getHighlightSegments,
  };
}
