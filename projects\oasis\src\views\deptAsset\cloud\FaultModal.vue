<template>
  <BasicModal title="设备报障" :width="700" :footer="null" :maskClosable="false" @register="registerModal">
    <div class="my-6">
      <BasicForm v-if="!isSuccess" :schemas="formSchema" @register="registerForm">
        <template #screenshot>
          <div class="flex flex-col gap-2">
            <Checkbox v-model:checked="withScreenshot" class="w-fit">
              提交当前截图
            </Checkbox>
            <div v-if="withScreenshot && !!screenshot" class="w-fit overflow-hidden border border-FO-Container-Stroke2 rounded-4px">
              <Image :src="screenshot" alt="当前页面截图" width="100px" />
            </div>
          </div>
        </template>
      </BasicForm>
      <Result v-else status="success" subTitle="问题已提交，感谢你的反馈，我们会及时进行处理" />
    </div>
    <div class="flex justify-end gap-4">
      <Button :type="isSuccess ? 'primary' : 'default'" @click="closeModal">
        {{ isSuccess ? '确定' : '取消' }}
      </Button>
      <Button v-if="!isSuccess" type="primary" :loading="isLoading" @click="handleSubmit">
        提交
      </Button>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { Button, Checkbox, Image, message, Result } from 'ant-design-vue';
import type { ModalMethods } from '/@/components/Modal';
import { BasicModal, useModalInner } from '/@/components/Modal';
import type { FormSchema } from '/@/components/Form';
import { computed, onMounted, ref } from 'vue';
import { BasicForm, useForm } from '/@/components/Form';
import { createFault, getFaultTypes } from '/@/api/page/deptAsset';
import type { DeviceListItem, FaultListItem, FaultTypeListItem } from '/@/api/page/model/deptAssetModel';
import { useCloudDeviceStore, useCloudDeviceWebsocketStore } from './stores';
import { mtlUploadFile } from '/@/api/page/mtl/device';
import { dataURLtoFile } from '/@/utils/file/base64Conver';
import { ResultEnum } from '/@/enums/httpEnum';

defineEmits<{
  register: [methods: ModalMethods, uuid: number];
}>();

const cloudDeviceStore = useCloudDeviceStore();
const websocketStore = useCloudDeviceWebsocketStore();

const device = ref<DeviceListItem>();
const faultTypes = ref<FaultTypeListItem[]>([]);
const screenshot = ref<string>('');
const withScreenshot = ref<boolean>(true);
const isSuccess = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const formSchema = computed<FormSchema[]>(() => [
  {
    label: '问题类型',
    field: 'faultType',
    component: 'Select',
    componentProps: {
      placeholder: '请选择问题类型',
      options: faultTypes.value?.map((type) => ({
        label: type.value,
        value: type.type,
      })) || [],
    },
    required: true,
  },
  {
    label: '应用名称',
    field: 'installId',
    component: 'Select',
    itemProps: {
      extra: '如没有显示在最近上传的应用列表，可在问题描述中补充',
    },
    componentProps: {
      placeholder: '请选择应用名称',
      options: websocketStore.recentApps?.filter((app) => app.appName && app.version).map((app) => ({
        label: `${app.appName} (${app.version})`,
        value: app.id,
      })) || [],
    },
  },
  {
    label: '问题描述',
    field: 'desc',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入问题描述',
      maxLength: 200,
    },
    required: true,
  },
  ...screenshot.value
    ? [{
      label: ' ',
      field: 'screenshot',
      component: 'Input',
      slot: 'screenshot',
    }]
    : [],
]);

const [registerForm, { resetFields, validate }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 22 },
  showActionButtonGroup: false,
});

const [registerModal, { closeModal }] = useModalInner(async (data) => {
  isLoading.value = true;
  await resetFields();
  isSuccess.value = false;
  device.value = data.device;
  screenshot.value = data.screenshot;
  withScreenshot.value = true;
  isLoading.value = false;
});

async function getFaultTypesList() {
  try {
    const { list } = await getFaultTypes();
    faultTypes.value = list || [];
  } catch (error) {
    console.error('获取故障类型失败', error);
  }
}

async function handleSubmit() {
  try {
    const values = await validate();
    isLoading.value = true;

    const submitData: FaultListItem = {
      ...values,
      agent: cloudDeviceStore.agent?.name || '',
      deviceId: cloudDeviceStore.device?.ID || 0,
      screenshot: undefined,
    };
    // 如果勾选了提交截图，上传截图数据
    if (withScreenshot.value && screenshot.value) {
      const formData = new FormData();
      formData.append('file', dataURLtoFile(screenshot.value, 'screenshot.jpg'));
      formData.append('type', 'imageFiles');
      formData.append('isPrivate', 'false');
      const { data } = await mtlUploadFile({}, formData);
      if (!data.data) {
        message.error('上传截图失败');
        return;
      }
      submitData.screenshot = data.data;
    }

    const res = await createFault(submitData);

    if (res?.code === ResultEnum.API_ERROR) {
      return;
    }

    isSuccess.value = true;
  } catch (error) {
    console.error('提交报障失败', error);
  } finally {
    isLoading.value = false;
  }
}

onMounted(() => {
  getFaultTypesList();
});
</script>
