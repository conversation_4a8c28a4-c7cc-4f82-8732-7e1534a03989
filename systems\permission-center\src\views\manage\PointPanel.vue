<template>
  <div class="h-full w-full flex flex-col">
    <div class="mb-16px flex justify-between">
      <div />
      <div class="flex gap-12px">
        <Input v-model:value.trim="searchKw" allowClear placeholder="搜索权限">
          <template #prefix>
            <SearchIcon class="c-FO-Content-Icon1" />
          </template>
        </Input>
        <Button class="btn-fill-primary" @click="() => handleNewPoint()">
          <template #icon>
            <AddIcon />
          </template>
          新增权限
        </Button>
      </div>
    </div>
    <div class="flex-1 overflow-hidden">
      <BasicVxeTable ref="baseTableRef" :options="gridOptions" />
    </div>
    <PointModalHolder />
    <InterfaceConfigModalHolder />
  </div>
</template>

<script setup lang="tsx">
import { computed, nextTick, ref, useTemplateRef, watch } from 'vue';
import { Button, Dropdown, Input, Menu, MenuItem, message, Modal, Popover, Tooltip } from 'ant-design-vue';

import { useHighlight } from '../../composables/useHighlight';
import { match } from 'pinyin-pro';
import type { VxeGridInstance, VxeGridProps } from 'vxe-table';
import { BasicVxeTable, EllipsisText } from '@hg-tech/oasis-common';
import { useModalShow } from '@hg-tech/utils-vue';
import AddIcon from '../../assets/icons/fill-add.svg?component';
import MoreIcon from '../../assets/icons/fill-more.svg?component';
import EditIcon from '../../assets/icons/fill-edit.svg?component';
import DeleteIcon from '../../assets/icons/fill-delete.svg?component';
import WarningErrorIcon from '../../assets/icons/fill-warning-error.svg?component';
import SearchIcon from '../../assets/icons/fill-search.svg?component';
import InterfaceIcon from '../../assets/icons/fill-interface.svg?component';
import {
  type PermissionCheckPoint,
  type PermissionCheckPointCategory,
  createPermissionCheckPoint,
  createPermissionCheckPointCategory,
  deletePermissionCheckPoint,
  deletePermissionCheckPointCategory,
  fetchPermissionCheckPointApis,
  movePermissionCheckPoint,
  sortPermissionCheckPointCategory,
  updatePermissionCheckPoint,
  updatePermissionCheckPointCategory,
  updatePermissionCheckPointCategoryRelatePoints,
  updatePermissionCheckPointRelateApis,
} from '../../api/manage.ts';
import { UNCATEGORIZED_SYMBOL, usePermissionCategories } from '../../composables/usePermissionCategories.ts';
import type { PermissionAppInfo } from '../../api/app.ts';
import PointFormModal from './components/PointFormModal.vue';
import EditingCategoryNameInput from './components/EditingCategoryNameInput.vue';
import InterfaceConfigDrawer from '../app/components/InterfaceConfigDrawer.vue';
import ApiMethodTag from './components/ApiMethodTag.vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { compact, isNumber, without } from 'lodash';

const props = defineProps<{
  appId: PermissionAppInfo['id'];
  apiConfigEnabled: boolean;
}>();

// 使用权限分类 composable
const { categories, allPermissionPoints, loading, refreshData } = usePermissionCategories();

watch([() => props.appId, () => props.apiConfigEnabled], refreshTableData, { immediate: true });

async function refreshTableData() {
  if (props.appId) {
    await refreshData(props.appId);
  }
}

const NEW_CATEGORY_SYMBOL = -2;

type RenderRow = ({
  id: number | typeof UNCATEGORIZED_SYMBOL | undefined;
  uniqueKey: string;
  name: string;
})&({
  type: 'category';
  childCount: number;
  item?: PermissionCheckPointCategory;
} | {
  type: 'point';
  categoryId: number | typeof UNCATEGORIZED_SYMBOL;
  parentUniqueKey: string;
  code: string;
  desc: string;
  item: PermissionCheckPoint;
});

const searchKw = ref<string>('');
const tableRef = useTemplateRef<VxeGridInstance<RenderRow>>('baseTableRef');
const { getHighlightSegments } = useHighlight();

// 展开状态管理
const expandedCategoryKeys = ref<string[]>([]);
const isInitialized = ref<boolean>(false);

// 保存当前展开状态
function saveExpandedState() {
  if (tableRef.value) {
    const expandedRecords = tableRef.value.getTreeExpandRecords();
    expandedCategoryKeys.value = expandedRecords.map((record: RenderRow) => record.uniqueKey);
  }
}

// 恢复展开状态
async function restoreExpandedState() {
  if (tableRef.value && expandedCategoryKeys.value.length > 0) {
    await nextTick();
    const allRows = tableRef.value.getTableData().fullData as RenderRow[];
    const rowsToExpand = allRows.filter((row: RenderRow) =>
      expandedCategoryKeys.value.includes(row.uniqueKey),
    );
    if (rowsToExpand.length > 0) {
      tableRef.value.setTreeExpand(rowsToExpand, true);
    }
  }
}

const tableRows = computed<RenderRow[]>(() => {
  const restPoints = searchKw.value
    ? allPermissionPoints.value.filter((i) => {
      const kw = searchKw.value.toLowerCase();
      const name = i.name?.toLowerCase() || '';
      const code = i.code?.toLowerCase() || '';

      // 支持文字匹配和拼音匹配
      return name.includes(kw)
        || code.includes(kw)
        || match(i.name || '', kw)
        || match(i.code || '', kw);
    })
    : [...allPermissionPoints.value];

  const rows = categories.value.reduce((acc, item) => {
    const childrenNodes: RenderRow[] = [];
    if (item.resources?.length) {
      for (const point of item.resources) {
        const idx = restPoints.findIndex((i) => i.id === point.id);
        const restPoint = Object.assign({}, restPoints[idx], { categoryId: item.id });
        if (idx >= 0) {
          childrenNodes.push({
            id: point.id,
            uniqueKey: `point-${point.id}`,
            name: point.name,
            type: 'point',
            categoryId: item.id,
            parentUniqueKey: `category-${item.id}`,
            code: point.code,
            desc: point.description || '-',
            item: restPoint,
          } as RenderRow);

          // 删除已经添加的点
          restPoints.splice(idx, 1);
        }
      }
    }
    if (!searchKw.value || childrenNodes.length > 0) {
      // 有子节点才添加分类节点，除非搜索关键词不为空
      return [
        ...acc,
        {
          id: item.id,
          uniqueKey: `category-${item.id}`,
          name: item.name,
          type: 'category',
          childCount: item.resources?.length ?? 0,
          item,
        },
        ...childrenNodes,
      ] as RenderRow[];
    }
    return acc;
  }, [] as RenderRow[]);
  if (restPoints.length > 0) {
    const unCategorized = restPoints.map((point) => ({
      id: point.id,
      uniqueKey: `point-${point.id}`,
      name: point.name,
      type: 'point',
      categoryId: UNCATEGORIZED_SYMBOL,
      parentUniqueKey: `category-${UNCATEGORIZED_SYMBOL}`,
      code: point.code,
      desc: point.description || '-',
      item: point,
    } as RenderRow));
    rows.unshift({
      id: UNCATEGORIZED_SYMBOL,
      uniqueKey: `category-${UNCATEGORIZED_SYMBOL}`,
      name: '未分类',
      type: 'category',
      childCount: unCategorized.length,
    } as RenderRow);
    rows.push(...unCategorized);
  }
  return rows;
});
const editingRowId = ref<RenderRow['id'] | typeof NEW_CATEGORY_SYMBOL>();
const tableColumns = computed<VxeGridProps<RenderRow>['columns']>(() => [
  { type: null, width: 40, dragSort: true },
  {
    field: 'name',
    title: '权限名称',
    minWidth: 300,
    treeNode: true,
    slots: {
      default({ row }) {
        switch (row.type) {
          case 'category':
            if (row.id === editingRowId.value) {
              return (
                <EditingCategoryNameInput
                  initValue={row.name}
                  onFinished={(cancelled) => {
                    editingRowId.value = undefined;
                    if (!cancelled) {
                      refreshTableData();
                      message.success('保存成功');
                    }
                  }}
                  sentReq={async (name) => {
                    try {
                      const res = await updatePermissionCheckPointCategory({ appId: props.appId, categoryId: row.id }, { name });
                      return res?.data?.code === 0;
                    } catch (error) {
                      console.error(error);
                      return false;
                    }
                  }}
                />
              );
            }
            return (
              <span class="flex items-center">
                <span class="FO-Font-B14 mr-12px">{row.name}</span>
                {row.id !== UNCATEGORIZED_SYMBOL && (
                  <span class="category-row__actions max-w-0 flex items-center overflow-hidden opacity-0 transition-none group-hover:max-w-full group-hover:opacity-100 group-hover:transition-300">
                    <Tooltip title="分类下新增">
                      <Button class="btn-fill-text" onClick={() => handleNewPoint(row)} size="small">
                        {{
                          icon: () => (
                            <AddIcon
                              class="text-12px"
                            />
                          ),
                        }}
                      </Button>
                    </Tooltip>
                    <Dropdown>
                      {{
                        overlay: () => (
                          <Menu>
                            <MenuItem onClick={() => editingRowId.value = row.id}>
                              <span class="flex items-center gap-4px">
                                <EditIcon class="text-16px" />
                                <span>修改名称</span>
                              </span>
                            </MenuItem>
                            <MenuItem onClick={() => handleDeleteCategory(row)}>
                              <span class="flex items-center gap-4px">
                                <DeleteIcon class="text-16px" />
                                <span>删除分类</span>
                              </span>
                            </MenuItem>
                          </Menu>
                        ),
                        default: () => (
                          <Button class="btn-fill-text" size="small">
                            {{
                              icon: () => (
                                <MoreIcon
                                  class="text-12px"
                                  onClick={() => handleDeletePoint(row)}
                                />
                              ),
                            }}
                          </Button>
                        ),
                      }}
                    </Dropdown>
                  </span>
                )}
                <span class="category-row__child-count b-rd-4px bg-FO-Container-Fill2 px-8px py-2px transition-300 group-hover:max-w-0 group-hover:overflow-hidden group-hover:opacity-0 group-hover:transition-none">{row.childCount}</span>
              </span>
            );
          case 'point':
            return searchKw.value
              ? getHighlightSegments(row.name, searchKw.value).map((i) => <span class={i.highlight ? 'c-FO-Brand-Primary-Default' : ''}>{i.text}</span>)
              : row.name;
        }
      },
      footer() {
        if (editingRowId.value === NEW_CATEGORY_SYMBOL) {
          return (
            <EditingCategoryNameInput
              onFinished={(cancelled) => {
                editingRowId.value = undefined;
                if (!cancelled) {
                  refreshTableData();
                  message.success('保存成功');
                }
              }}
              sentReq={async (name) => {
                try {
                  const res = await createPermissionCheckPointCategory({ appId: props.appId }, { name });
                  return res?.data?.code === 0;
                } catch (error) {
                  console.error(error);
                  return false;
                }
              }}
            />
          );
        }
        return (
          <Button
            class="ml--38px c-FO-Content-Text2"
            onClick={() => editingRowId.value = NEW_CATEGORY_SYMBOL}
            type="link"
          >
            {{
              icon: () => <PlusOutlined class="c-primary" />,
              default: () => <span>新增分类</span>,
            }}
          </Button>
        );
      },
    },
  },
  {
    field: 'code',
    title: '权限Code',
    showOverflow: 'tooltip',
    slots: {
      default({ row }) {
        if (row.type !== 'point') {
          return null;
        }
        return searchKw.value
          ? getHighlightSegments(row.code, searchKw.value).map((i) => <span class={i.highlight ? 'c-FO-Brand-Primary-Default' : ''}>{i.text}</span>)
          : row.code;
      },
    },
  },
  { field: 'desc', title: '权限说明', showOverflow: 'tooltip' },
  ...(props.apiConfigEnabled
    ? [{
      field: 'apis',
      title: '接口配置',
      minWidth: 200,
      slots: {
        default({ row }: { row: RenderRow }) {
          if (row.type !== 'point') {
            return null;
          }

          const apis = row.item.apis?.filter((i) => i?.id) || [];
          if (apis.length === 0) {
            return <span class="c-FO-Content-Text4">暂无接口</span>;
          }

          const renderApiPopoverContent = (api: typeof apis[0]) => (
            <div class="max-w-280px">
              <EllipsisText class="FO-Font-B14 mb-4px c-FO-Content-Text1">{api.name}</EllipsisText>
              <div class="flex items-center gap-8px">
                <ApiMethodTag method={api.method || 'get'} />
                <EllipsisText class="FO-Font-R14 c-FO-Content-Text2">{api.path || '-'}</EllipsisText>
              </div>
            </div>
          );

          return (
            <div class="flex flex-wrap gap-4px">
              {apis.map((api) => (
                <Popover
                  content={renderApiPopoverContent(api)}
                  key={api.id}
                  trigger="hover"
                >
                  <span class="FO-Font-B14 inline-block cursor-pointer b-rd-4px bg-FO-Container-Fill3 px-6px py-2px c-FO-Content-Text2 transition-colors hover:bg-FO-Container-Fill2">
                    {api.name}
                  </span>
                </Popover>
              ))}
            </div>
          );
        },
      },
    }]
    : []),
  {
    field: 'actions',
    title: '操作',
    slots: {
      default({ row }) {
        if (row.type === 'category') {
          // 分类节点不显示操作按钮
          return [];
        }

        return (
          <div class="flex items-center gap-4px">
            <Tooltip title="编辑权限">
              <Button class="btn-fill-text" onClick={() => handleEditPoint(row)}>
                {{ icon: () => <EditIcon class="text-16px" /> } }
              </Button>
            </Tooltip>
            {props.apiConfigEnabled && (
              <Tooltip title="接口配置">
                <Button class="btn-fill-text" onClick={() => handleInterfaceConfig(row)}>
                  {{ icon: () => <InterfaceIcon class="text-16px" /> }}
                </Button>
              </Tooltip>
            )}
            <Tooltip title="删除权限">
              <Button class="btn-fill-text" onClick={() => handleDeletePoint(row)}>
                {{ icon: () => <DeleteIcon class="text-16px" /> } }
              </Button>
            </Tooltip>
          </div>
        );
      },
    },
  },
] as VxeGridProps<RenderRow>['columns']);
// 监听数据加载状态，确保只有在数据完全加载完成后才进行初始化
watch([tableRows, loading], async ([rows, isLoading]) => {
  await nextTick();
  // 只有在数据加载完成且有数据时才处理
  if (!isLoading && rows.length > 0) {
    if (!isInitialized.value) {
      // 初始化时展开所有分类
      tableRef.value?.setAllTreeExpand(true);
      saveExpandedState();
      isInitialized.value = true;
    } else {
      // 后续数据变化时恢复之前的展开状态
      await restoreExpandedState();
    }
  }
});

const gridOptions = computed(() => ({
  showFooter: true,
  rowConfig: {
    keyField: 'uniqueKey',
    drag: true,
    isHover: true,
  },
  rowClassName: 'group',
  height: 'auto',
  columnConfig: {
    resizable: false,
  },
  rowDragConfig: {
    isPeerDrag: true,
    dragStartMethod({ row }) {
      // 拖拽开始时保存展开状态
      saveExpandedState();
      // 只有分类拖拽时才收起最后一个分类，权限点拖拽时保持展开状态
      if (row.type === 'category' && tableRef.value) {
        // 获取所有分类行（不包括未分类）
        const allRows = tableRef.value.getTableData().fullData as RenderRow[];
        const categoryRows = allRows.filter((r: RenderRow) =>
          r.type === 'category' && r.id !== UNCATEGORIZED_SYMBOL,
        );

        // 找到最后一个分类并收起
        if (categoryRows.length > 0) {
          const lastCategory = categoryRows[categoryRows.length - 1];
          tableRef.value.setTreeExpand([lastCategory], false);
        }
      }
      return true; // 允许拖拽继续
    },
    async dragEndMethod({ newRow, dragRow, offsetIndex, dragPos, dragToChild }) {
      await restoreExpandedState();

      if (newRow.id === UNCATEGORIZED_SYMBOL && dragPos === 'top') {
        // 不允许拖拽到未分类节点的上面
        return false;
      }
      if (newRow.type === 'point' && newRow.categoryId === UNCATEGORIZED_SYMBOL && dragRow.type === 'point' && dragRow.categoryId === UNCATEGORIZED_SYMBOL) {
        // 未分类节点不能排序
        return false;
      }
      if (dragToChild && newRow.type === 'point') {
        // 不允许拖拽到权限点节点
        return false;
      }

      if (dragToChild && newRow.type === 'category' && dragRow.type === 'point') {
        // 拖拽到子节点仅允许拖拽到分类节点（跨分类移动权限点）
        try {
          const res = await movePermissionCheckPoint({ appId: props.appId }, { resourceId: dragRow.id, toCategoryId: newRow.id });
          if (res.data?.code === 0) {
            await refreshTableData();
            // 权限点跨分类移动成功，恢复展开状态
            await restoreExpandedState();
            return true;
          }
        } catch (error) {
          console.error(error);
        }
        return false;
      }

      if (newRow.type === dragRow.type) {
        if (newRow.type === 'category') {
          // 分类排序逻辑
          const categoryIds = categories.value.map((i) => i.id);
          const movedId = dragRow.id;
          const movedIndex = categoryIds.indexOf(movedId);
          categoryIds.splice(movedIndex, 1);
          const targetIndex = categoryIds.indexOf(newRow.id);
          categoryIds.splice(targetIndex + offsetIndex, 0, movedId);

          try {
            const res = await sortPermissionCheckPointCategory({ appId: props.appId }, { order: categoryIds });
            if (res?.data?.code === 0) {
              await refreshTableData();
              await restoreExpandedState();
              return true;
            }
          } catch (error) {
            console.error(error);
          }
          return false;
        } else {
          // 权限点排序 - 只允许同分类内排序
          if (dragRow.type !== 'point' || newRow.type !== 'point') {
            return false;
          }

          if (dragRow.categoryId !== newRow.categoryId) {
            // 不允许跨分类排序权限点
            return false;
          }

          // 获取目标分类下的所有权限点ID
          const targetCategory = categories.value.find((cat) => cat.id === newRow.categoryId);
          const currentPointIds = compact(targetCategory?.resources?.map((point) => point.id)) || [];

          // 重新计算排序：移除被拖拽的权限点，然后在新位置插入
          const draggedPointId = dragRow.id as number;
          const filteredIds = without(currentPointIds, draggedPointId);
          const targetIndex = filteredIds.indexOf(newRow.id as number);
          const insertIndex = targetIndex + offsetIndex;

          // 在指定位置插入被拖拽的权限点ID
          filteredIds.splice(insertIndex, 0, draggedPointId);

          try {
            const res = await updatePermissionCheckPointCategoryRelatePoints({ appId: props.appId, categoryId: newRow.categoryId }, filteredIds);
            if (res?.data?.code === 0) {
              await refreshTableData();
              // 权限点排序成功
              return true;
            }
          } catch (error) {
            console.error(error);
          }
          return false;
        }
      }

      // 不允许拖拽到不同类型的节点
      return false;
    },
    visibleMethod({ row }) {
      // 隐藏未分类分类节点的拖拽按钮
      if (row.id === UNCATEGORIZED_SYMBOL) {
        return false;
      }
      // 隐藏未分类下权限点的拖拽按钮
      if (row.type === 'point' && row.categoryId === UNCATEGORIZED_SYMBOL) {
        return false;
      }
      return true;
    },
  },
  treeConfig: {
    transform: true,
    rowField: 'uniqueKey',
    parentField: 'parentUniqueKey',
  },
  columns: tableColumns.value,
  footerData: [{}],
  data: tableRows.value,
}) as VxeGridProps<RenderRow>);

const [PointModalHolder, showCreatePointModal] = useModalShow(PointFormModal);
const [InterfaceConfigModalHolder, showInterfaceConfigDrawer] = useModalShow(InterfaceConfigDrawer);
async function handleNewPoint(row?: RenderRow) {
  const category = row?.type === 'category' ? row?.item : undefined;
  await showCreatePointModal({
    appId: props.appId,
    title: '新增权限',
    initData: { categoryId: category?.id },
    disabledFields: category?.id ? ['categoryId'] : [],
    async sentReq(formValue) {
      const res = await createPermissionCheckPoint({ appId: props.appId }, formValue);
      return res.data?.code === 0 ? res.data?.data : undefined;
    },
  });

  message.success('保存成功');
  searchKw.value = '';
  await refreshTableData();
}
async function handleEditPoint(row: RenderRow) {
  if (row.type !== 'point') {
    return;
  }
  await showCreatePointModal({
    appId: props.appId,
    title: '编辑权限',
    initData: row.item,
    disabledFields: ['code'],
    async sentReq(formValue) {
      const res = await updatePermissionCheckPoint({ appId: props.appId, resourceId: row.item.id }, formValue);
      return res.data?.code === 0 ? res.data?.data : undefined;
    },
  });

  message.success('保存成功');
  await refreshTableData();
}
async function handleDeletePoint(row: RenderRow) {
  if (row.type !== 'point') {
    return;
  }
  Modal.confirm({
    icon: null,
    width: 496,
    okText: '删除',
    okButtonProps: {
      type: 'primary',
      danger: true,
    },
    cancelButtonProps: {
      // @ts-expect-error cancelButtonProps支持class但没有类型定义
      class: 'btn-fill-default',
    },
    centered: true,
    closable: true,
    title: () => {
      return (
        <div class="flex items-center">
          <WarningErrorIcon class="c-FO-Functional-Error1-Default" />
          <div class="F0-Font-B16 ml-8px">删除权限</div>
        </div>
      );
    },
    content() {
      return (
        <div class="mt-12px pb-8px c-FO-Content-Text2">
          删除权限【{row.item?.name}】，此操作不可恢复，请谨慎操作。
        </div>
      );
    },
    async onOk() {
      try {
        const res = await deletePermissionCheckPoint({ appId: props.appId, resourceId: row.item.id }, {});
        if (res.data?.code === 0) {
          await refreshTableData();
          message.success('操作成功');
        }
      } catch (error) {
        console.error(error);
      }
    },
  });
}

async function handleInterfaceConfig(row: RenderRow) {
  if (row.type !== 'point') {
    return;
  }
  // 获取接口列表
  const apiListRes = await fetchPermissionCheckPointApis({ appId: props.appId }, {});

  const currentPointApis = row.item.apis?.filter((i) => i?.id) ?? [];
  const selectedApiIds = currentPointApis.map((api) => api.id).filter((id) => isNumber(id)) as number[];

  // 显示接口配置抽屉
  await showInterfaceConfigDrawer({
    apiList: apiListRes.data?.data ?? [],
    selectedApiIds,
    async sentReq(selectedApiIds: number[]) {
      if (row.item.id) {
        try {
          const res = await updatePermissionCheckPointRelateApis(
            { appId: props.appId, resourceId: row.item.id },
            selectedApiIds,
          );
          return res?.data?.code === 0;
        } catch (error) {
          console.error(error);
          return false;
        }
      }
      return false;
    },
  });

  message.success('操作成功');
  await refreshTableData();
}

async function handleDeleteCategory(row: RenderRow) {
  if (row.type !== 'category' || !row.item?.id) {
    return;
  }
  Modal.confirm({
    icon: () => null,
    width: 496,
    okText: '删除',
    okButtonProps: {
      type: 'primary',
      danger: true,
    },
    cancelButtonProps: {
      // @ts-expect-error cancelButtonProps支持class但没有类型定义
      class: 'btn-fill-default',
    },
    centered: true,
    closable: true,
    title: () => {
      return (
        <div class="flex items-center">
          <WarningErrorIcon class="c-FO-Functional-Error1-Default" />
          <div class="F0-Font-B16 ml-8px">删除分类</div>
        </div>
      );
    },
    content() {
      return (
        <div class="mt-12px pb-8px c-FO-Content-Text2">
          该操作不会删除分类下的权限，权限将归入“未分类”
        </div>
      );
    },
    async onOk() {
      try {
        const res = await deletePermissionCheckPointCategory({ appId: props.appId, categoryId: row.item?.id }, {});
        if (res.data?.code === 0) {
          await refreshTableData();
          message.success('操作成功');
        }
      } catch (error) {
        console.error(error);
      }
    },
  });
}
</script>
