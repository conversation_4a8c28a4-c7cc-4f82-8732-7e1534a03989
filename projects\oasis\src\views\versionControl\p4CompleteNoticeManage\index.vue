<template>
  <PageWrapper :class="prefixCls" headerSticky @back="goBack">
    <template #title>
      <div v-track:v="'7chqzyjufd'" class="flex items-center">
        提交完成通知：{{ curStream?.description }}
        <a-button
          v-if="curStream?.hasNoticeRule"
          class="custom-rounded-btn ml-4"
          noIcon
          size="small"
          @click="handleDeleteCompleteNotice()"
        >
          删除提交完成通知
        </a-button>
      </div>
    </template>
    <div
      v-if="(curStream?.hasNoticeRule && showNoticeList && !isLoading) || isConfig || isNew"
      class="flex"
    >
      <ScrollContainer class="w-400px!">
        <div class="bg-FO-Container-Fill1 h-fit max-h-[calc(100vh_-_168px)] w-400px rounded-md p-4">
          <div class="h-24px flex items-center justify-between">
            <div class="flex items-center font-bold">
              通知规则
            </div>
            <a-button
              v-if="isPreview || isConfig"
              v-track="'b5dhuvczzz'"
              shape="round"
              class="custom-rounded-btn ml-2"
              size="small"
              @click="handleCreate()"
            >
              <Icon icon="ant-design:plus-outlined" :size="14" />
              添加
            </a-button>
          </div>
          <div class="c-FO-Content-Text2 my-3 text-xs">
            配置通知规则，提交完成后将通知发送至对应的群聊
          </div>
          <div :class="`${prefixCls}__tab`">
            <div
              v-for="item in showNoticeList"
              :key="item.ID"
              :class="`${prefixCls}__tab-item`"
              :active="item.UUID === activeNoticeID || item.ID === activeNoticeID"
              :isNew="!!item.UUID"
              @click="handleNoticeChange(item.UUID || item.ID!)"
            >
              <div class="w-250px flex items-center">
                <Icon icon="devGuard-auditor|svg" />
                <EllipsisText class="ml-2">
                  {{ item.name || '未命名（请给通知规则命名）' }}
                </EllipsisText>
              </div>
              <div>
                <APopconfirm title="确定要删除该通知规则吗" @confirm.stop="handleDelete(item)">
                  <a-button type="text" size="small" @click.stop>
                    <Icon class="!c-FO-Functional-Error1-Default" icon="ant-design:delete-outlined" />
                  </a-button>
                </APopconfirm>
              </div>
            </div>
          </div>
        </div>
      </ScrollContainer>
      <ScrollContainer class="flex-1">
        <div class="ml-4 max-h-[calc(100vh_-_168px)]">
          <div
            v-if="isConfig || showNoticeList.length === 0"
            class="bg-FO-Container-Fill1 rounded-md p-4 text-center"
          >
            该分支暂无通知规则，请添加
          </div>
          <PreviewPage
            v-if="showNoticeList.length > 0"
            :activeNotice="activeNotice"
            :showNoticeList="showNoticeList"
            :isPreview="isPreview"
            :isUpdate="isUpdate"
            :isNew="isNew"
            :isConfig="isConfig"
            :chatList="chatList"
            :initItem="initItem"
            @editOrRevert="handleEditOrRevert"
            @success="handleSuccess"
          />
        </div>
      </ScrollContainer>
    </div>
    <div
      v-if="!curStream?.hasNoticeRule && !isCloneOpen && !isConfig && !isNew && !isLoading"
      class="bg-FO-Container-Fill1 m-4 rounded-md p-4"
    >
      <div class="my-10 w-full flex flex-col items-center justify-center">
        <div class="c-FO-Content-Text2 text-lg">
          该分支未配置提交完成通知
        </div>
        <div class="mt-6">
          <a-button type="primary" @click="isConfig = true">
            配置
          </a-button>
          <a-button class="ml-3" type="primary" @click="handleGoClone">
            克隆其他分支的配置
          </a-button>
        </div>
      </div>
    </div>
    <div v-if="isCloneOpen" class="bg-FO-Container-Fill1 m-4 rounded-md p-4">
      <div class="my-10 flex items-center justify-center">
        <div>
          <Icon icon="icon-park-outline:comment" :size="80" class="c-FO-Brand-Primary-Default" />
        </div>
        <div class="ml-8">
          <div class="c-FO-Content-Text1">
            从已存在分支中克隆提交完成通知：
          </div>
          <div class="mt-4 flex">
            <div class="w-80px">
              <div class="text-sm font-bold">
                分支：
              </div>
              <div class="c-FO-Content-Text2 text-xs">
                克隆分支
              </div>
            </div>
            <BasicForm class="w-270px" @register="registerForm" />
          </div>
          <div class="mt-4">
            <a-button
              type="primary"
              size="small"
              class="w-16!"
              :loading="isCloneLoading"
              @click="handleClone"
            >
              克隆
            </a-button>
            <a-button size="small" class="ml-4 w-16!" @click="isCloneOpen = false">
              取消
            </a-button>
          </div>
        </div>
      </div>
    </div>
    <DeleteModal @register="registerDeleteModal" @success="handleSuccess" />
  </PageWrapper>
</template>

<script lang="ts" setup>
import { Popconfirm as APopconfirm } from 'ant-design-vue';
import { nextTick, onBeforeMount, ref, unref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useP4DepotStream } from '../p4PermissionManage/hook';
import { cloneCompleteNoticeFormSchema } from '../p4PermissionManage/streams/fastAddStream/fastAddStream.data';
import DeleteModal from './DeleteModal.vue';
import PreviewPage from './Preview.vue';
import type { ItemsItem, P4CompleteNoticeListItem, StreamsListItem } from '/@/api/page/model/p4Model';
import type { FeishuChatListItem } from '/@/api/page/model/systemModel';
import {
  copyP4CompleteNotice,
  deleteCompleteNotice,
  getP4CompleteNoticeByID,
  getP4CompleteNoticeListByPage,
} from '/@/api/page/p4';
import { getAppChatList } from '/@/api/page/system';
import { BasicForm, useForm } from '/@/components/Form/index';
import Icon from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import { PageWrapper } from '/@/components/Page';
import { useTimeoutFn } from '/@/hooks/core/useTimeout';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useTabs } from '/@/hooks/web/useTabs';
import { useAppStore } from '/@/store/modules/app';
import { useP4StoreWithOut } from '/@/store/modules/p4';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { buildNumberUUID } from '/@/utils/uuid';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { ScrollContainer } from '/@/components/Container';

const { prefixCls } = useDesign('p4-complete-notice-manage');
const { currentRoute, push } = useRouter();
const userStore = useUserStoreWithOut();
const p4Store = useP4StoreWithOut();
const { createMessage } = useMessage();
const { refreshPage } = useTabs();
const { allStreamList, getAllStreamList } = useP4DepotStream();
const [registerDeleteModal, { openModal: openDeleteModal }] = useModal();
const streamID = Number(unref(currentRoute).params.stream_id);
const depotID = Number(unref(currentRoute).params.id);
const isConfig = ref<boolean>(false);
const isCloneOpen = ref<boolean>(false);
const isPreview = ref<boolean>(false);
const isUpdate = ref<boolean>(false);
const isNew = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const isCloneLoading = ref<boolean>(false);
const activeNoticeID = ref<number>();
const curStream = ref<StreamsListItem>();
const showNoticeList = ref<P4CompleteNoticeListItem[]>([]);
const activeNotice = ref<P4CompleteNoticeListItem>();
const chatList = ref<FeishuChatListItem[]>([]);
const initItem = ref<ItemsItem>();
const appStore = useAppStore();

const [registerForm, { validate, updateSchema }] = useForm({
  schemas: cloneCompleteNoticeFormSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
});

function handleGoClone() {
  isCloneOpen.value = true;
  nextTick(async () => {
    await updateSchema({
      field: 'copyFromStreamID',
      componentProps: {
        options: allStreamList.value?.filter((e) => e.hasNoticeRule && e.streamType !== 4),
      },
    });
  });
}

async function handleClone() {
  const values = await validate();

  if (!userStore.getProjectId) {
    return;
  }

  if (values.copyFromStreamID) {
    isCloneLoading.value = true;
    await copyP4CompleteNotice(userStore.getProjectId, {
      fromStreamID: values.copyFromStreamID,
      toStreamID: streamID,
    });
    p4Store.setIsAfterCopy(true);
    createMessage.success('正在克隆，请稍候...');
    useTimeoutFn(() => refreshPage(), 2000);
  }
}

function handleEditOrRevert(isEdit: boolean, id?: number) {
  if (isEdit) {
    handleEdit();
  } else {
    handleSuccess(false, id);
  }
}

function handleEdit() {
  isPreview.value = false;
  isUpdate.value = true;
  isNew.value = false;
}

// 删除通知规则
async function handleDelete(record: Recordable) {
  if (!userStore.getProjectId) {
    return;
  }

  if (record.UUID) {
    showNoticeList.value = showNoticeList.value.filter((e) => e.UUID !== record.UUID);
  } else {
    await deleteCompleteNotice(userStore.getProjectId, record.ID);
  }

  await handleSuccess(true);
}

// 获取指定ID的通知规则
async function getNotice(id?: number) {
  if (!userStore.getProjectId) {
    return;
  }

  if (showNoticeList.value.length > 0) {
    activeNoticeID.value = id || showNoticeList.value[0].ID!;

    const { commitNoticeRule } = await getP4CompleteNoticeByID(
      userStore.getProjectId,
      activeNoticeID.value!,
    );

    activeNotice.value = commitNoticeRule;
  }
}

// 切换通知规则
async function handleNoticeChange(id: number) {
  if (id === activeNoticeID.value) {
    return;
  }

  if (!isPreview.value && !isUpdate.value) {
    showNoticeList.value = showNoticeList.value.filter((e) => !e.UUID);
  }

  isPreview.value = true;
  isNew.value = false;
  activeNoticeID.value = id;
  await getNotice(id);
}

// 添加
function handleCreate() {
  const newID = buildNumberUUID();
  const newNotice = {
    UUID: newID,
    name: '(新增中, 未保存)',
  };

  showNoticeList.value.push(newNotice);
  activeNoticeID.value = newID;
  isPreview.value = false;
  isUpdate.value = false;
  isNew.value = true;
  isConfig.value = false;
}

async function handleSuccess(isSubmit?: boolean, id?: number) {
  if (isSubmit) {
    showNoticeList.value = [];
    activeNotice.value = undefined;
  }

  isPreview.value = true;
  isUpdate.value = false;
  isNew.value = false;
  await getCurStream();
  await getCompleteNoticeList();
  await getNotice(id);
}

// 删除提交完成通知
function handleDeleteCompleteNotice() {
  openDeleteModal(true);
}

// 获取提交完成通知列表
async function getCompleteNoticeList() {
  if (!userStore.getProjectId) {
    return;
  }

  const { list } = await getAllPaginationList((p) => getP4CompleteNoticeListByPage(userStore.getProjectId, {
    ...p,
    streamID,
  }));

  showNoticeList.value = list?.length > 0 ? list : [];
}

async function getLarkChatList() {
  const { list } = await getAppChatList({ robot: 'submit' });

  chatList.value = list || [];
  initItem.value = {
    ruleType: 1,
    ruleFile: '',
    chatID: chatList.value[0]?.chat_id,
  };
}

async function getCurStream() {
  await getAllStreamList(depotID, false);
  curStream.value = allStreamList.value?.find((e) => e.ID === streamID);

  if (curStream.value) {
    p4Store.setCurStream(curStream.value);
  }
}

// 页面左侧点击返回链接时的操作
function goBack() {
  push({ name: 'P4Depots' });
}

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      // 切换项目后返回仓库列表
      push({ name: 'P4Depots' });
    }
  },
);

onBeforeMount(async () => {
  appStore.setPageLoadingAction(true);
  isLoading.value = true;
  await getLarkChatList();
  await getCurStream();
  await getCompleteNoticeList();
  await getNotice();
  isPreview.value = true;
  appStore.setPageLoadingAction(false);
  isLoading.value = false;
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-p4-complete-notice-manage';
.@{prefix-cls} {
  &__tab {
    margin: 8px 0;

    &-item {
      display: flex;
      justify-content: space-between;
      margin: 8px 0;
      padding: 8px;
      border: 1px solid transparent;
      border-radius: 6px;
      cursor: pointer;

      &:hover {
        border-color: @FO-Container-Stroke1;
      }

      &[active='true'] {
        border-color: @FO-Brand-Primary-Default;
      }

      &[isNew='true'] {
        border-style: dashed;
        color: @FO-Functional-Error1-Default;
      }
    }
  }
}
</style>
