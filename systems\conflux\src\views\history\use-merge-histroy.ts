import { createSharedComposable } from '@vueuse/core';
import { useRouteQuery } from '@vueuse/router';
import { useMergeHome } from '../home/<USER>';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { computed, ref, watch } from 'vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../api';
import {
  type MergeServiceGetMergeRecordParams,
  type MergeServiceGetOperationsParams,
  type MergeV1MergeState,
  MergeV1MergeRecordOrderBy,
  MergeV1OrderDirection,
} from '@hg-tech/api-schema-merge';
import { debounce } from 'lodash';
import { useRouter } from 'vue-router';

const useMergeHistory = createSharedComposable(() => {
  const currentRuleId = useRouteQuery<string | undefined>('ruleId', undefined, { mode: 'replace' });
  const forgeonConfig = useForgeonConfigStore(store);
  const currentProjectId = computed(() => forgeonConfig.currentProjectId);
  const router = useRouter();
  const routeQuery = computed(() => router.currentRoute.value.query);
  const searchForm = ref<Omit<MergeServiceGetMergeRecordParams, 'ruleId' | 'onlyFailed'>>({
    page: 1,
    pageSize: 20,
    id: currentProjectId.value!,
    submitter: undefined,
    orderBy: MergeV1MergeRecordOrderBy.MERGE_RECORD_ORDER_BY_INVALID,
    orderDirection: MergeV1OrderDirection.ORDER_DIRECTION_INVALID,
    clStart: routeQuery.value?.clStart as string || undefined,
    clEnd: routeQuery.value?.clEnd as string || undefined,
    submitTimeStart: routeQuery.value?.submitTimeStart as string || undefined,
    submitTimeEnd: routeQuery.value?.submitTimeEnd as string || undefined,
    state: (routeQuery.value.state as string)?.split(',').filter(Boolean) as MergeV1MergeState[] || [],
  });
  const { initHomeData, streamList, ruleList, currentBranchMap } = useMergeHome();
  const { data: historyResponse, loading: historyListLoading, execute: getHistoryList, reset: clearHistoryList } = useLatestPromise(mergeApi.v1.mergeServiceGetMergeRecord);
  const { data: operationResponse, loading: operationListLoading, execute: getOperationList } = useLatestPromise(mergeApi.v1.mergeServiceGetOperations);

  const fetchHistoryList = debounce(async () => {
    if (!currentRuleId.value || !currentProjectId.value) {
      return;
    }
    await getHistoryList({
      ...searchForm.value,
      ruleId: currentRuleId.value,
      id: currentProjectId.value,
    }, {});
  }, 100);

  const initHistoryData = async () => {
    if (!currentProjectId.value) {
      return;
    }
    await initHomeData();
  };

  const fetchOperationList = async (params: Omit<MergeServiceGetOperationsParams, 'id'>) => {
    if (!currentProjectId.value) {
      return;
    }
    try {
      await getOperationList(
        {
          ...params,
          id: currentProjectId.value,
        },
        {},
      );
      if (operationResponse.value && operationResponse.value.data) {
        return operationResponse.value.data;
      }
    } catch (error) {
      console.error('Failed to fetch operation list:', error);
    }
  };

  watch(currentProjectId, (newProjectId, oldValue) => {
    if (oldValue && oldValue !== newProjectId) {
      // 如果项目ID发生变化，清除当前规则ID
      currentRuleId.value = undefined;
    }
    if (newProjectId) {
      clearHistoryList();
      initHistoryData();
    }
  }, { immediate: true });

  watch([currentRuleId, ruleList], ([rId, rList]) => {
    // 如果路由中的ruleId不在ruleList中，使用ruleList第一个规则
    if (rList?.length && (!rId || !rList.some((rule) => rule.id === rId))) {
      currentRuleId.value = rList[0]?.id;
    }
  }, { immediate: true });

  return {
    searchForm,
    currentRuleId,
    currentProjectId,
    ruleList,
    currentBranchMap,
    streamList,
    historyListLoading,
    historyList: computed(() => historyResponse.value?.data?.data?.records || []),
    historyTotal: computed(() => Number.parseInt(historyResponse.value?.data?.data?.total || '0')),
    operationListLoading: computed(() => operationListLoading.value),
    operationList: computed(() => operationResponse.value?.data?.data?.operations || []),
    operationTotal: computed(() => Number.parseInt(operationResponse.value?.data?.data?.total || '0')),
    initHistoryData,
    fetchHistoryList,
    fetchOperationList,
  };
});

export {
  useMergeHistory,
};
