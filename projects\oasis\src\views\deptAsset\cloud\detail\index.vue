<template>
  <div class="h-full w-full flex flex-col">
    <Layout class="flex-none">
      <LayoutHeader>
        <template #title="{ currentTitle }">
          <div class="FO-Font-B18">
            {{ currentTitle }}
          </div>
          <div
            class="FO-Font-R12 ml-2 rd-r-full rd-t-full rd-bl-0 rd-br-full bg-FO-Brand-Secondary-Default px-2 py-1 c-FO-Brand-Primary-Default"
          >
            Beta
          </div>
        </template>
      </LayoutHeader>
    </Layout>
    <div class="min-h-0 w-full flex flex-1 flex-col">
      <div class="flex items-center justify-between gap-4 bg-FO-Container-Fill1 p-4">
        <div class="flex items-center gap-2">
          <Icon :icon="ArrowLeft" :size="20" class="cursor-pointer" @click="() => handleGoBack()" />
          <EllipsisText class="text-16px font-bold !max-w-[200px]">
            {{ cloudDeviceStore.device?.deviceName }}
          </EllipsisText>
          <Icon
            v-if="cloudDeviceStore.device" :icon="Info" :size="16"
            class="cursor-pointer c-FO-Content-Text2 opacity-80" @click="() => handleInfo()"
          />
          <Button :loading="faultLoading" @click="() => handleFault()">
            <template #icon>
              <Icon :icon="CautionIcon" :size="16" />
            </template>
            报障
          </Button>
        </div>
        <div class="flex items-center gap-2">
          <div class="rd-2 bg-FO-Container-Fill2 px-2 py-1 c-FO-Content-Text2">
            占用时长{{ showOccupyingTime }}分钟，30分钟无操作将自动退出
          </div>
          <div class="w-120px flex items-center gap-1">
            <span class="whitespace-nowrap c-FO-Content-Text2">已使用：</span>{{ formatTime(useSeconds) }}
          </div>

          <Button
            v-if="showOccupyingTime < MAX_RENEW_MINUTES"
            class="!b-none !bg-FO-Functional-Success1-Default !c-FO-Content-Components1 !hover:bg-FO-Functional-Success1-Hover"
            @click="() => handleRenew()"
          >
            续期
          </Button>
          <Tooltip v-else>
            <template #title>
              已达占用时长上限
            </template>
            <Button class="!b-none" disabled>
              续期
            </Button>
          </Tooltip>

          <Button danger type="primary" @click="() => handleEndUse()">
            结束使用
          </Button>
        </div>
      </div>
      <div class="min-h-0 flex flex-1 gap-4 px-4 py-2">
        <Splitpanes>
          <Pane class="h-full w-1/3 rd-2 bg-FO-Container-Fill1" :minSize="30" :size="36.8">
            <PhoneArea />
          </Pane>
          <Pane class="h-full min-w-0 flex-1 rd-2 bg-FO-Container-Fill1" :minSize="36">
            <FunctionArea />
          </Pane>
        </Splitpanes>
      </div>
      <DetailModal @register="registerDetailModal" />
      <DurationModal @register="registerDurationModal" @success="handleDurationChange" />
      <FaultModal @register="registerFaultModal" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, h, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { stopUseDevice } from '/@/api/page/mtl/device';
import { EllipsisText } from '/@/components/EllipsisText';
import { Icon } from '/@/components/Icon';
import ArrowLeft from '@iconify-icons/icon-park-outline/arrow-left';
import Info from '@iconify-icons/icon-park-solid/info';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { Button, Layout, Modal, notification, Tooltip } from 'ant-design-vue';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { useModal } from '/@/components/Modal';
import DetailModal from '../../apply/detail/DetailModal.vue';
import { Pane, Splitpanes } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
import PhoneArea from './PhoneArea.vue';
import FunctionArea from './FunctionArea.vue';
import { useCloudDeviceStore, useCloudDeviceWebsocketStore } from '../stores';
import DurationModal from '../UseDurationModal.vue';
import FaultModal from '../FaultModal.vue';
import dayjs from 'dayjs';
import { useIntervalFn } from '@vueuse/core';
import { useScreenshot } from '../composables/useScreenshot';
import CautionIcon from '@iconify-icons/icon-park-outline/caution';
import { MtlAgentStatus } from '/@/api/page/mtl/model/deviceModel';
import { isNumber } from 'lodash-es';
import { EXPIRE_THRESHOLD, MAX_RENEW_MINUTES } from '../device.data';
import { sendEvent } from '../../../../service/tracker/index.ts';
import LayoutHeader from '/@/layouts/default/header/index.vue';

const { push } = useRouter();
const userStore = useUserStoreWithOut();
const cloudDeviceStore = useCloudDeviceStore();
const websocketStore = useCloudDeviceWebsocketStore();
const { getBodyScreenshot } = useScreenshot();

const [registerDetailModal, { openModal: openDetailModal }] = useModal();
const [registerDurationModal, { openModal: openDurationModal }] = useModal();
const [registerFaultModal, { openModal: openFaultModal }] = useModal();

const useSeconds = ref(0);
const showOccupyingTime = ref<number>(0);
const showRemainTime = computed(() => {
  const remainTime = showOccupyingTime.value - Math.floor(useSeconds.value / 60);
  return remainTime > 0 ? remainTime : 0;
});

const faultLoading = ref(false);

const showExpireModal = computed(() => {
  return isNumber(showRemainTime.value) && showRemainTime.value <= EXPIRE_THRESHOLD;
});

/** 返回 */
function goBack() {
  push({ name: PlatformEnterPoint.CloudDevice });
}

/** 停止使用 */
async function stopUse() {
  if (!cloudDeviceStore.mtlDevice?.udId) {
    return;
  }
  websocketStore.isUserClosed = true;
  try {
    await stopUseDevice({ udId: cloudDeviceStore.mtlDevice?.udId }, {});
  } finally {
    goBack();
  }
}

/** 返回 */
function handleGoBack() {
  Modal.confirm({
    title: '是否结束使用设备',
    okText: '结束使用',
    cancelText: '保持占用',
    closable: true,
    cancelButtonProps: {
      // @ts-expect-error cancelButtonProps支持class但没有类型定义
      class: '!bg-FO-Functional-Success1-Default hover:!bg-FO-Functional-Success1-Hover',
      type: 'primary',
      onClick: () => {
        goBack();
      },
    },
    okButtonProps: {
      // todo 修复全局样式异常后可删除此行
      // @ts-expect-error okButtonProps支持class但没有类型定义
      class: '!bg-FO-Functional-Error1-Default hover:!bg-bg-FO-Functional-Error1-Hover',
      danger: true,
    },
    onOk: () => {
      stopUse();
    },
  });
}

/** 结束使用 */
function handleEndUse() {
  Modal.confirm({
    title: '确认结束使用吗？',
    icon: null,
    okText: '确认',
    okButtonProps: {
      // todo 修复全局样式异常后可删除此行
      // @ts-expect-error okButtonProps支持class但没有类型定义
      class: '!bg-FO-Functional-Error1-Default hover:!bg-bg-FO-Functional-Error1-Hover',
      danger: true,
    },
    onOk: () => {
      stopUse();
    },
  });
}

/** 查看设备详情 */
function handleInfo() {
  openDetailModal(true, {
    cloudDevice: cloudDeviceStore.mtlDevice,
    deviceID: cloudDeviceStore.device?.ID,
    readOnly: true,
  });
}

/** 续期 */
function handleRenew() {
  openDurationModal(true, {
    device: cloudDeviceStore.device,
    isRenew: true,
  });
}

/** 报障 */
async function handleFault() {
  faultLoading.value = true;
  try {
    const screenshot = await getBodyScreenshot();
    openFaultModal(true, {
      device: cloudDeviceStore.device,
      recentApps: websocketStore.recentApps || [],
      screenshot,
    });
  } finally {
    faultLoading.value = false;
  }
}

// 格式化时间为 HH:mm:ss
function formatTime(seconds: number): string {
  return dayjs.duration(seconds, 'seconds').format('HH:mm:ss');
}

function calculateUseSeconds() {
  const currentTime = dayjs().valueOf();
  const endUseTime = cloudDeviceStore.mtlDevice?.deviceOccupy?.endUseTime ?? 0;
  showOccupyingTime.value = cloudDeviceStore.mtlDevice?.deviceOccupy?.planUseMinutes ?? 0;
  useSeconds.value = Math.max(0, Math.floor(showOccupyingTime.value * 60 - (endUseTime - currentTime) / 1000));
}

function handleDurationChange(duration?: number) {
  calculateUseSeconds();
  if (duration) {
    sendEvent('cloud_device_renew', { time_length: duration });
  }
}

onMounted(async () => {
  await cloudDeviceStore.getDevice();
  await cloudDeviceStore.getMtlDeviceInfo();
  // 如果设备未被申请，或者当前用户不是设备使用人，则提示无法使用
  if (!cloudDeviceStore.mtlDevice?.deviceOccupy || (cloudDeviceStore.mtlDevice?.deviceOccupy?.username && cloudDeviceStore.mtlDevice.deviceOccupy.username !== userStore.getUserInfo?.userName)) {
    Modal.warning({
      title: '未申请当前设备，无法使用',
      onOk: () => {
        goBack();
      },
    });
    return;
  }
  if (cloudDeviceStore.mtlDevice?.agentId) {
    await cloudDeviceStore.getAgentInfo(cloudDeviceStore.mtlDevice.agentId);
  }
  if (cloudDeviceStore.agent?.status === MtlAgentStatus.Offline) {
    Modal.warning({
      title: '设备维护中，请稍后',
      onOk: () => {
        goBack();
      },
    });
    return;
  }
  calculateUseSeconds();
  websocketStore.openSocket();
  useIntervalFn(() => useSeconds.value++, 1000, { immediate: true });
});

onBeforeUnmount(() => {
  websocketStore.closeWebsocket(false);
  notification.close('notification');
});
watch(() => showExpireModal.value, (val) => {
  if (val) {
    notification.warning({
      key: 'notification',
      message: '占用到期提醒',
      description: '当前设备仅剩5分钟使用时长，到期后将自动退出',
      placement: 'top',
      duration: 30,
      class: 'w-400px',
      btn: () =>
        h(
          Button,
          {
            class: '!b-none !bg-success !text-white !hover:bg-success/80',
            onClick: () => {
              notification.close('notification');
              handleRenew();
            },
          },
          { default: () => '续期' },
        ),
    });
  }
});
</script>
