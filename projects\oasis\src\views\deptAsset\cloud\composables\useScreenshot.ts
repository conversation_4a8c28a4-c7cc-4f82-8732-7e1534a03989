import type { Ref } from 'vue';
import { useCloudDeviceWebsocketStore } from '../stores';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import type { ScreenshotItem } from '../types';
import { sendEvent } from '../../../../service/tracker/index.ts';
import { deferMs } from '@hg-tech/utils';

export function useScreenshot() {
  const websocketStore = useCloudDeviceWebsocketStore();
  async function getBodyScreenshot(): Promise<string | undefined> {
    await deferMs(0); // 等待 loading 动画更新
    try {
      // 1. 获取屏幕共享流
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          displaySurface: 'browser',
        },
        audio: false, // 不需要音频
        // @ts-expect-error 支持的实验性特性
        preferCurrentTab: true,
        selfBrowserSurface: 'include',
      });
      // 2. 创建video元素接收流
      const video = document.createElement('video');
      video.srcObject = stream;
      await video.play(); // 等待视频开始播放
      // 3. 创建canvas捕获单帧
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      // 4. 绘制当前帧到canvas
      canvas.getContext('2d')?.drawImage(video, 0, 0, canvas.width, canvas.height);
      // 5. 停止所有轨道
      stream.getTracks().forEach((track) => track.stop());
      // 6. 获取图像数据（可选）
      return canvas.toDataURL('image/png');
    } catch (err) {
      console.error('获取屏幕截图失败', err);
      message.error('获取屏幕截图失败');
    }
  }

  // 获取视频截图
  const getVideoScreenshot = (
  ) => {
    const canvas = websocketStore.canvasRef as HTMLCanvasElement;
    const canvasCtx = canvas?.getContext('2d');
    const video = websocketStore.scrcpyVideoRef as HTMLVideoElement;
    // 默认生成图片大小
    let w;
    let h;
    if (websocketStore.directionStatus === 0 || websocketStore.directionStatus === 180) {
      if (websocketStore.screenMode === 'Scrcpy') {
        w = websocketStore.imgWidth;
        h = websocketStore.imgHeight;
      } else {
        w = 369;
        h = 800;
      }
    } else if (websocketStore.screenMode === 'Scrcpy') {
      w = websocketStore.imgHeight;
      h = websocketStore.imgWidth;
    } else {
      w = 800;
      h = 369;
    }
    canvas.width = w;
    canvas.height = h;
    canvasCtx?.drawImage(
      video,
      0,
      0,
      video.videoWidth,
      video.videoHeight,
      0,
      0,
      w,
      h,
    );
    return canvas.toDataURL('image/png', 1);
  };

  // 获取图片URL
  const getImgUrl = (
  ) => {
    let imageUrl;
    if (websocketStore.oldBlob) {
      const blob = new Blob([websocketStore.oldBlob], { type: 'image/jpeg' });
      const URL = window.URL || window.webkitURL;
      imageUrl = URL.createObjectURL(blob);
    } else {
      imageUrl = getVideoScreenshot();
    }
    return imageUrl;
  };

  // 设置图片数据
  const setImgData = (
    imgUrl: Ref<string>,
  ) => {
    const imageUrl = getImgUrl();
    const image = new Image();
    image.src = imageUrl;
    imgUrl.value = imageUrl;
    const canvas = websocketStore.debugPicRef;
    if (!canvas) {
      return;
    }
    image.onload = () => {
      canvas.width = image.width;
      canvas.height = image.height;
    };
    return imageUrl;
  };

  // 快速截图
  const quickCap = () => {
    let imageUrl: string;
    if (websocketStore.oldBlob) {
      const blob = new Blob([websocketStore.oldBlob], { type: 'image/jpeg' });
      const URL = window.URL || window.webkitURL;
      imageUrl = URL.createObjectURL(blob);
    } else {
      imageUrl = getVideoScreenshot();
    }
    const image = new Image();
    websocketStore.screenshotList.push({
      url: imageUrl,
      time: dayjs(),
    });
    image.src = imageUrl;
    sendEvent('cloud_device_screenshot');
  };

  // 下载图片
  const downloadImg = (screenshot: ScreenshotItem) => {
    const currentTime = screenshot.time.format('YYYY-MM-DD_HH-mm-ss');
    const link = document.createElement('a');
    fetch(screenshot.url)
      .then((res) => res.blob())
      .then((blob) => {
        link.href = URL.createObjectURL(blob);
        link.download = `${currentTime}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
    message.success('图片下载成功！');
  };

  // 清除截图
  const removeScreen = () => {
    websocketStore.screenshotList = [];
  };

  return {
    getBodyScreenshot,
    getVideoScreenshot,
    getImgUrl,
    setImgData,
    quickCap,
    downloadImg,
    removeScreen,
  };
}
