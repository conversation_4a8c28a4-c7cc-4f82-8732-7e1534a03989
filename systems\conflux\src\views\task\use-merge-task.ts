import { createSharedComposable } from '@vueuse/core';
import { useMergeHome } from '../home/<USER>';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { computed, ref, watch } from 'vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../api';
import {
  type MergeServiceGetMergeRecordParams,
  type MergeV1FailReason,
  MergeV1MergeRecordOrderBy,
  MergeV1OrderDirection,
} from '@hg-tech/api-schema-merge';
import { useUserAuthStore } from '../../store/modules/userAuth';
import { useRouteQuery } from '@vueuse/router';
import { debounce } from 'lodash';
import { useRouter } from 'vue-router';

const useMergeTask = createSharedComposable(() => {
  const currentRuleId = useRouteQuery<string | undefined>('ruleId', undefined, { mode: 'replace' });
  const forgeonConfig = useForgeonConfigStore(store);
  const { userProfile } = useUserAuthStore(store);
  const currentProjectId = computed(() => forgeonConfig.currentProjectId);
  const router = useRouter();
  const routeQuery = computed(() => router.currentRoute.value.query);
  const searchForm = ref<Omit<MergeServiceGetMergeRecordParams, 'ruleId' | 'onlyFailed'>>({
    page: 1,
    pageSize: 20,
    id: currentProjectId.value!,
    handler: userProfile?.userName ? [userProfile?.userName] : undefined,
    state: [],
    orderBy: MergeV1MergeRecordOrderBy.MERGE_RECORD_ORDER_BY_INVALID,
    orderDirection: MergeV1OrderDirection.ORDER_DIRECTION_INVALID,
    clStart: routeQuery.value?.clStart as string || undefined,
    clEnd: routeQuery.value?.clEnd as string || undefined,
    submitTimeStart: routeQuery.value?.submitTimeStart as string || undefined,
    submitTimeEnd: routeQuery.value?.submitTimeEnd as string || undefined,
    failReason: (routeQuery.value.failReason as string)?.split(',').filter(Boolean) as MergeV1FailReason[] || [],
  });
  const { initHomeData, streamList, ruleList, currentBranchMap } = useMergeHome();
  const { data: taskResponse, loading: taskLoading, execute: getTaskList, reset: clearTaskList } = useLatestPromise(mergeApi.v1.mergeServiceGetMergeRecord);
  const { data: fileResponse, execute: getTaskFiles, reset: clearTaskFiles } = useLatestPromise(mergeApi.v1.mergeServiceGetResolveDetail);
  const { data: workspaceResponse, loading: workspaceLoading, execute: getWorkspaceList } = useLatestPromise(mergeApi.v1.mergeServiceGetUserWorkspace);
  const { data: skipReasonsResponse, execute: getSkipResons } = useLatestPromise(mergeApi.v1.mergeServiceGetSkipReasons);
  const { execute: getOperationList } = useLatestPromise(mergeApi.v1.mergeServiceGetOperations);

  const fetchTaskList = debounce(async () => {
    if (!currentRuleId.value || !currentProjectId.value) {
      return;
    }
    await getTaskList({
      ...searchForm.value,
      ruleId: currentRuleId.value,
      id: currentProjectId.value,
      onlyFailed: true,
    }, {});
  }, 100);

  const getWorkSpace = async () => {
    if (!currentRuleId.value || !currentProjectId.value || workspaceLoading.value) {
      return;
    }
    try {
      await getWorkspaceList({ ruleId: currentRuleId.value, id: currentProjectId.value }, {});
    } catch (error) {
      console.error('Failed to fetch workspace list:', error);
    }
  };

  const initTaskData = async () => {
    if (!currentProjectId.value) {
      return;
    }
    await initHomeData();
    await Promise.all([
      getSkipResons({ id: currentProjectId.value }, {}),
      getWorkSpace(),
    ]);
  };

  const fetchTaskFiles = async (recordId: string) => {
    if (!recordId || !currentProjectId.value) {
      return;
    }

    try {
      const response = await getTaskFiles({ id: currentProjectId.value!, recordId }, {});
      if (response && response.data) {
        return response.data.data;
      }
    } catch (error) {
      console.error('Failed to fetch task files:', error);
    }
  };

  /**
   * 获取最新的本地处理的操作状态信息
   */
  const getLatestLocalResolver = async (recordId: string) => {
    if (!recordId || !currentProjectId.value) {
      return;
    }
    try {
      const response = await getOperationList({
        recordId,
        page: 1,
        pageSize: 20,
        onlyMine: true,
        type: 'OPERATION_TYPE_LOCAL_PROCESS',
        id: currentProjectId.value,
      }, {});
      if (response && response.data) {
        return response.data.data?.operations?.[0];
      }
    } catch (error) {
      console.error('Failed to fetch local resolver state:', error);
    }
  };

  watch(currentProjectId, (newProjectId, oldValue) => {
    if (oldValue && oldValue !== newProjectId) {
      // 如果项目ID发生变化，清除当前规则ID
      currentRuleId.value = undefined;
    }
    if (newProjectId) {
      clearTaskList();
      initTaskData();
    }
  }, { immediate: true });

  watch([currentRuleId, ruleList], ([rId, rList]) => {
    // 如果路由中的ruleId不在ruleList中，使用ruleList第一个规则
    if (rList?.length && (!rId || !rList.some((rule) => rule.id === rId))) {
      currentRuleId.value = rList[0]?.id;
    }
  }, { immediate: true });

  return {
    streamList,
    currentProjectId,
    searchForm,
    currentRuleId,
    ruleList,
    currentBranchMap,
    taskList: computed(() => taskResponse.value?.data?.data?.records || []),
    taskLoading,
    taskTotal: computed(() => Number.parseInt(taskResponse.value?.data?.data?.total || '0')),
    fileList: computed(() => fileResponse.value?.data?.data?.resolves || []),
    workspaceOptions: computed(() => (workspaceResponse.value?.data?.data?.workspaces && workspaceResponse.value?.data?.data?.workspaces.length > 0
      ? workspaceResponse.value?.data?.data?.workspaces?.map((item) => ({
        label: item,
        value: item,
      }))
      : [])),
    skipReasons: computed(() => skipReasonsResponse.value?.data?.data?.reasons || []),
    getWorkSpace,
    fetchTaskList,
    fetchTaskFiles,
    clearTaskFiles,
    getLatestLocalResolver,
  };
});

export {
  useMergeTask,
};
