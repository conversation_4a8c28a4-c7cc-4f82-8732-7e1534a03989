import type { DropMenu } from 'src/components/Dropdown/index.ts';
import type { FormSchema } from 'src/components/Form/index.ts';

export const groupEditList: DropMenu[] = [
  {
    text: '删除',
    event: 'delete',
  },
];

export const auditFormSchema: FormSchema[] = [
  {
    field: 'needReview',
    label: '有权Review的干员',
    component: 'BorderBox',
    subLabel: '只有将干员添加至该组, 为该干员配置的Review权限才能生效',
    children: [
      {
        field: 'members',
        label: '添加干员',
        component: 'UserSelect',
        componentProps: {
          isMultiple: true,
          fieldNames: {
            label: 'displayName',
            value: 'userName',
          },
        },
      },
      {
        field: 'subGroups',
        label: '添加干员组',
        component: 'Select',
        slot: 'subGroups',
        rules: [{ required: true, message: '请选择干员组', type: 'array' }],
      },
    ],
  },
  {
    field: 'noReview',
    label: '不需要被Review和关注的干员',
    component: 'BorderBox',
    children: [
      {
        field: 'ignoreUsers',
        label: '添加干员',
        component: 'UserSelect',
        componentProps: {
          isMultiple: true,
          fieldNames: {
            label: 'displayName',
            value: 'userName',
          },
        },
      },
      {
        field: 'ignoreGroups',
        label: '添加干员组',
        component: 'Select',
        slot: 'subGroups',
      },
    ],
  },
];

export const cloneFormSchema: FormSchema[] = [
  {
    field: 'copyFromReviewProjectID',
    label: '源分支',
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: 'name',
      placeholder: '请选择源分支',
      allowClear: true,
      getPopupContainer: () => document.body,
    },
    required: true,
  },
];

export const lockSwarmFormSchema: FormSchema[] = [
  {
    field: 'reviewGroup',
    label: '审查团成员',
    component: 'BorderBox',
    children: [
      {
        field: 'lockReviewerIds',
        label: '添加干员',
        component: 'UserSelect',
        componentProps: {
          isMultiple: true,
          fieldNames: {
            label: 'displayName',
            value: 'ID',
          },
        },
      },
      {
        field: 'lockReviewerGroups',
        label: '添加干员组',
        component: 'TreeSelect',
        slot: 'lockReviewerGroups',
      },
    ],
  },
];

export function msgFormSchema(name: string, botName: string) {
  return [
    {
      label: (
        <div>
          <b>{name}通知群聊</b>
          <span class="ml-1 !text-xs">
            <span>（需先将</span>
            <b class="mx-1">{botName}</b>
            <span>添加至群中）</span>
          </span>
        </div>
      ),
      field: 'chatID',
      component: 'Select',
      slot: 'chatID',
    },
  ] as FormSchema[];
}

export interface SelectOption {
  label: string;
  value: string;
}

export const reviewUrgentModalFormSchema: FormSchema[] = [
  {
    field: 'urgentLock',
    label: '审批通知加急',
    component: 'Switch',
    defaultValue: false,
    componentProps: {
      checkedChildren: '开',
      unCheckedChildren: '关',
    },
  },
  {
    field: 'urgentReview',
    label: 'Review通知加急',
    component: 'Switch',
    defaultValue: false,
    componentProps: {
      checkedChildren: '开',
      unCheckedChildren: '关',
    },
  },
  {
    field: 'blockUnauth',
    label: '屏蔽灰色通知',
    component: 'Switch',
    defaultValue: false,
    componentProps: {
      checkedChildren: '开',
      unCheckedChildren: '关',
    },
  },
];
