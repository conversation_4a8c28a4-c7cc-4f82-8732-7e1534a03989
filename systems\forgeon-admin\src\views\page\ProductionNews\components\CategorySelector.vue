<template>
  <Select
    v-model:value="valueProxy"
    :loading="loading"
    :options="categoryOptions"
    placeholder="请选择分类"
  />
</template>

<script setup lang="ts">
import { Select } from 'ant-design-vue';
import { useVModel } from '@vueuse/core';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { forgeonAdminApi } from '../../../../api/index.ts';
import { computed } from 'vue';

const props = defineProps<{
  value?: string;
}>();
const emit = defineEmits<{
  (event: 'update:value', value?: string): void;
}>();

const valueProxy = useVModel(props, 'value', emit);
const { data, loading, execute } = useLatestPromise(forgeonAdminApi.api.configServiceGetConfigNewsCategory);
const categoryOptions = computed(() => data.value?.data?.data?.categories?.map((i) => ({
  value: i,
  label: i,
})) ?? []);

execute({}, {});
</script>
