<template>
  <Modal
    width="680px"
    :open="show"
    :title="title"
    :maskClosable="false"
    :destroyOnClose="true"
    :centered="true"
    :afterClose="modalDestroy"
    @ok="handleConfirm"
    @cancel="() => modalCancel()"
  >
    <Form :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
      <FormItem label="标题" v-bind="validateInfos.title">
        <Input v-model:value="formValue.title" placeholder="请输入" />
      </FormItem>
      <FormItem label="图片" v-bind="validateInfos.image">
        <Uploader
          v-model:url="formValue.image"
          accept="image/*"
          :previewMap="imagePreviewMap"
          :maxSize="200"
        />
        <div class="c-gray">
          图片要求：建议大小1582×220，不超过 200k
        </div>
      </FormItem>
      <FormItem label="跳转链接" v-bind="validateInfos.url">
        <Input v-model:value="formValue.url" placeholder="请输入跳转页面url" />
      </FormItem>
      <FormItem label="排序" v-bind="validateInfos.position">
        <InputNumber v-model:value="formValue.position" placeholder="请输入" :min="0" :step="1" />
      </FormItem>
    </Form>
  </Modal>
</template>

<script setup lang="tsx">
import type { RuleObject } from 'ant-design-vue/es/form/interface';
import { ref } from 'vue';
import { Form, FormItem, Input, InputNumber, Modal } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import type { BannerFormData } from './FormData.ts';
import Uploader from '../../../components/Uploader.vue';
import type { ConfigV1BannerItem } from '@hg-tech/api-schema-forgeon-admin';

const props = defineProps<ModalBaseProps & {
  title?: string;
  imagePreviewMap?: { [url: string]: string };
  initData?: ConfigV1BannerItem;
  sentReq: (formValue: BannerFormData) => Promise<unknown>;
}>();

const formValue = ref<BannerFormData>({
  title: props.initData?.title,
  image: props.initData?.image,
  url: props.initData?.url,
  status: props.initData?.status as BannerFormData['status'],
  position: props.initData?.position ?? 100,
});

const formRule = ref<Record<keyof BannerFormData, RuleObject[]>>({
  title: [{ required: true, message: '请输入标题' }],
  image: [{ required: true, message: '请上传图片' }],
  url: [{ pattern: /^(\w+:)?\/\//, message: '请输入完整URL，如 https://www.hypergryph.com/' }],
  status: [],
  position: [{ required: true, message: '请输入排序' }],
});

const { validate, validateInfos } = Form.useForm(formValue, formRule);

async function handleConfirm() {
  const formRes = await validate();
  await props.sentReq(formRes);
  await props.modalConfirm();
}
</script>
