<template>
  <Layout.Header :class="getHeaderClass">
    <!-- left start -->
    <div :class="`${prefixCls}-left`">
      <Tooltip v-if="getShowMenuUnfoldBtn" destroyTooltipOnHide title="展开" placement="top">
        <MenuUnfold class="unfold-icon cursor-pointer" @click="onHandleExpand" />
      </Tooltip>
      <slot name="title" :currentTitle="currentTitle">
        <div class="FO-Font-B18">
          {{ currentTitle }}
        </div>
      </slot>
    </div>
    <!-- left end -->

    <!-- action  -->
    <div :class="`${prefixCls}-action`">
      <AppHelpDoc v-if="getShowDoc" :class="`${prefixCls}-action__item`" />
      <slot name="projectSelect">
        <AppProjectSelect
          v-if="hasToken && getShowProjectSelect"
          :class="`${prefixCls}-project-select`"
          :theme="getDarkMode"
          :showAll="projectShowAllOption"
        />
      </slot>
    </div>
  </Layout.Header>
</template>

<script lang="ts" setup>
import { computed, unref } from 'vue';
import { propTypes } from '/@/utils/propTypes';
import { Layout, Tooltip } from 'ant-design-vue';
import { AppHelpDoc, AppProjectSelect } from '/@/components/Application';
import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';
import { useAppInject } from '/@/hooks/web/useAppInject';
import { useDesign } from '/@/hooks/web/useDesign';
import { useRouter } from 'vue-router';
import { useUserStore } from '/@/store/modules/user';
import { useAppStore } from '/@/store/modules/app';
import { PlatformEnterPoint, useForgeOnSider } from '@hg-tech/oasis-common';
import MenuUnfold from '/@/assets/svg/forgeon/menu-unfold.svg?component';
import { FORGEON_MENU_FOLDED_KEY } from '/@/enums/cacheEnum';
import { Persistent } from '/@/utils/cache/persistent';

const props = defineProps({
  fixed: propTypes.bool,
});
const { prefixCls } = useDesign('layout-header');
const { activeMenuItem, collapsed, showSubSider } = useForgeOnSider();

const {
  getShowDoc,
  getShowProjectSelect,
} = useHeaderSetting();
const { getDarkMode } = useRootSetting();

const { getIsMobile } = useAppInject();

const { currentRoute } = useRouter();

const appStore = useAppStore();

const userStore = useUserStore();
const hasToken = userStore.getToken;

const getShowMenuUnfoldBtn = computed(() => {
  return unref(collapsed) && unref(showSubSider);
});

const getHeaderClass = computed(() => {
  const theme = appStore.getDarkMode;

  return [
    prefixCls,
    {
      [`${prefixCls}--fixed`]: props.fixed,
      [`${prefixCls}--mobile`]: unref(getIsMobile),
      [`${prefixCls}--${theme}`]: theme,
    },
  ];
});

const currentTitle = computed(() => {
  return activeMenuItem.value?.title;
});

const projectShowAllOption = computed(() => {
  return currentRoute.value.name === PlatformEnterPoint.TrackingAnalysis;
});

function onHandleExpand() {
  collapsed.value = false;
  Persistent.setLocal(FORGEON_MENU_FOLDED_KEY, '0');
}
</script>

<style lang="less">
@header-trigger-prefix-cls: ~'hypergryph-layout-header-trigger';
@header-prefix-cls: ~'hypergryph-layout-header';
@breadcrumb-prefix-cls: ~'hypergryph-layout-breadcrumb';
@logo-prefix-cls: ~'hypergryph-app-logo';
@logo-width: 32px;

.ant-layout .@{header-prefix-cls} {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: @header-height;
  padding: 0;
  background-color: @header-bg-color;
  line-height: @header-height;
  width: 100%;
  border-bottom: 1px solid @FO-Container-Stroke1;
  min-width: 440px;

  &--fixed {
    position: fixed;
    z-index: 500;
    top: 0;
    left: 0;
    width: calc(100% - 46px);
  }

  &-logo {
    min-width: 192px;
    height: @header-height;
    padding: 0 10px;
    font-size: 14px;

    img {
      width: @logo-width;
      height: @logo-width;
      margin-right: 2px;
    }
  }

  &-left {
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 18px;
    font-weight: 500;
    padding-left: 20px;

    .unfold-icon {
      box-sizing: content-box;
      height: 20px;
      width: 20px;
      padding: 4px;
      margin-right: 8px;
      &:hover {
        background-color: @FO-Container-Stroke2;
        border-radius: 4px;
      }
    }

    .@{header-trigger-prefix-cls} {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 1px 10px 0;
      cursor: pointer;

      .anticon {
        font-size: 16px;
      }

      &.light {
        &:hover {
          background-color: @header-light-bg-hover-color;
        }

        svg {
          fill: #000;
        }
      }

      &.dark {
        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }

  &-action {
    display: flex;
    // padding-right: 12px;
    align-items: center;
    min-width: 128px;
    padding-right: 20px;
    flex-direction: row-reverse;

    &__item {
      display: flex !important;
      align-items: center;
      font-size: 1.2em;
      margin-left: 16px;
      cursor: pointer;

      .ant-badge {
        height: @header-height;
        line-height: @header-height;
      }

      .ant-badge-dot {
        top: 14px;
        right: 2px;
      }
    }

    span[role='img'] {
      padding: 0 8px;
    }
  }

  &--light {
    .@{header-prefix-cls}-logo {
      color: @FO-Content-Text1;

      &:hover {
        background-color: @header-light-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        color: @FO-Content-Text1;

        .app-iconify {
          font-size: 16px !important;
        }

        &:hover {
          background-color: @header-light-bg-hover-color;
          border-radius: 4px;
        }
      }

      &-icon,
      span[role='img'] {
        color: @FO-Content-Text1;
      }
    }
  }

  &--dark {
    .@{header-prefix-cls}-logo {
      &:hover {
        background-color: @header-dark-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        color: @FO-Content-Components1;

        .app-iconify {
          font-size: 16px !important;
        }

        .ant-badge {
          span {
            color: @FO-Content-Components1;
          }
        }

        &:hover {
          background-color: @header-dark-bg-hover-color;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
