import { type Ref, computed, watch } from 'vue';
import { type LocationQuery, type RouteLocationRaw, useRouter } from 'vue-router';
import { mapValues } from 'lodash';

export function useSyncToRouteQuery<T extends Record<string, any>>(
  payload: Ref<T>,
  toRoute: (query: Record<keyof T, string>, currentQuery: LocationQuery) => RouteLocationRaw,
  options?: {
    pick?: (keyof T)[];
  },
) {
  const router = useRouter();
  const routeQuery = computed<Record<keyof T, string>>(() => mapValues(payload.value, (v, k) => {
    if (options?.pick && !options.pick.includes(k as keyof T)) {
      return undefined;
    }
    if (Array.isArray(v)) {
      return v.join(',') || undefined;
    }
    if (typeof v === 'object' && v !== null) {
      return JSON.stringify(v);
    }
    if (v === undefined || v === null) {
      return undefined;
    }
    return v.toString();
  }));

  watch(
    routeQuery,
    (v) => router.replace(toRoute(v, router.currentRoute.value.query)),
    { immediate: true },
  );
}
