import Icon from '@ant-design/icons-vue';
import { Button, Drawer, Form, message, Select } from 'ant-design-vue';
import { type PropType, computed, defineComponent, ref, watch } from 'vue';
import { type MergeV1MergeRecord, type MergeV1Operation, MergeV1OperationStatus } from '@hg-tech/api-schema-merge';
import { DrawerHeader } from '../components/drawer-header';
import { useMergeTask } from '../use-merge-task';
import { safeParseJSON } from '@hg-tech/utils';

import { useUserAuthStore } from '../../../store/modules/userAuth';
import { store } from '../../../store/pinia';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../../api';
import { ForgeonTemplateRenderer } from '@hg-tech/oasis-common';

import Close from '../../../assets/svg/Close.svg?component';
import BasicStrokeProcessing from '../../../assets/svg/BasicStrokeProcessing.svg?component';
import BasicFillCheck2 from '../../../assets/svg/BasicFillCheck2.svg?component';
import Subtract from '../../../assets/svg/Subtract.svg?component';
import SystemFillWorkspace from '../../../assets/svg/SystemFillWorkspace.svg?component';

const LocalResolver = defineComponent({
  props: {
    visible: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    data: {
      default: () => ({}),
      type: Object as PropType<MergeV1MergeRecord>,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const authStore = useUserAuthStore(store);
    const { currentProjectId, workspaceOptions, currentRuleId, ruleList, getLatestLocalResolver } = useMergeTask();
    const { data: shelveResponse, execute: createShelve } = useLatestPromise(mergeApi.v1.mergeServiceCreateShelve);

    const latestLocalResolver = ref<MergeV1Operation | undefined>(undefined);
    const formRef = ref();
    const form = ref<{
      workspace: string | undefined;
    }>({
      workspace: undefined,
    });
    const processInterval = ref<NodeJS.Timeout | null>(null);
    const open = computed({
      get: () => props.visible,
      set: (value) => {
        emit('update:visible', value);
      },
    });
    const currentRule = computed(() => ruleList.value.find((rule) => rule.id === currentRuleId.value));

    const onClose = () => {
      open.value = false;
    };

    const onSubmit = () => {
      formRef.value.validate().then(async () => {
        await createShelve({
          id: currentProjectId.value!,
        }, {
          recordId: props.data.id,
          workspace: form.value.workspace,
        });
        if (props.data?.id) {
          latestLocalResolver.value = await getLatestLocalResolver(props.data.id);
        }
        message.success('创建 Shelve 请求已发送，请稍候查看结果');
      }).catch((e: any) => {
        console.warn('表单验证失败', e);
      });
    };

    const renderShelveStatus = () => {
      switch (latestLocalResolver.value?.status) {
        case MergeV1OperationStatus.OPERATION_STATUS_PROCESSING:
          return (
            <span>
              <Icon class="mr-8px animate-spin font-size-16px c-FO-Brand-Primary-Default" component={<BasicStrokeProcessing />} />
              已发送创建Shelve的请求,请稍候...
            </span>
          );
        case MergeV1OperationStatus.OPERATION_STATUS_SUCCESS:
          return (
            <span>
              <Icon class="mr-8px font-size-16px c-FO-Datavis-Green1" component={<BasicFillCheck2 />} />
              <ForgeonTemplateRenderer
                customRender={{
                  workspace: (value: string) => <span class="FO-Font-B14">{value}</span>,
                  shelveCl: (value: string) => <span class="FO-Font-B14">{value}</span>,
                }}
                data={safeParseJSON(latestLocalResolver.value.operationDetail) ?? {}}
                delimiter={['${', '}']}
                template="成功创建 Shelve CL - ${shelveCl} 到 Workspace - ${workspace}"
              />
              ，请前往P4V查看。点击查看
              <a
                class="ml-4px c-FO-Content-Link-Default"
                href="https://hypergryph.feishu.cn/wiki/WjISwxuhBiLRK4k8wJhcSLAnnbg"
                target="_blank"
              >
                详细教程
              </a>
            </span>
          );
        case MergeV1OperationStatus.OPERATION_STATUS_FAILED:
          return (
            <span>
              <Icon class="mr-8px font-size-16px c-FO-Datavis-Green1" component={<Subtract />} />
              创建Shelve失败,可能是该CL已成功合并。点击查看
              <a
                class="ml-4px c-FO-Content-Link-Default"
                href="https://hypergryph.feishu.cn/wiki/WjISwxuhBiLRK4k8wJhcSLAnnbg"
                target="_blank"
              >
                详细教程
              </a>
            </span>
          );
        default:
          return null;
      }
    };

    watch(() => props.data.id, async (newVal) => {
      if (newVal && open.value) {
        latestLocalResolver.value = await getLatestLocalResolver(newVal);
        if (!latestLocalResolver.value) {
          return;
        }
        const { workspace: prvWorkspace } = safeParseJSON(latestLocalResolver.value.operationDetail) as { workspace: string };
        form.value.workspace = prvWorkspace;
      }
    }, { immediate: true });

    watch([latestLocalResolver, open], async () => {
      // 如果当前有正在处理的本地合并操作，则开启轮询
      if (latestLocalResolver.value && latestLocalResolver.value.status === MergeV1OperationStatus.OPERATION_STATUS_PROCESSING) {
        if (processInterval.value) {
          clearInterval(processInterval.value);
        }
        processInterval.value = setInterval(
          async () => {
            if (!props.data.id || !open.value) {
              clearInterval(processInterval.value!);
              processInterval.value = null;
              return;
            }
            latestLocalResolver.value = await getLatestLocalResolver(props.data.id);
            // 如果本地合并操作状态不是处理中，则停止轮询
            if (latestLocalResolver.value?.status !== MergeV1OperationStatus.OPERATION_STATUS_PROCESSING) {
              clearInterval(processInterval.value!);
              processInterval.value = null;
            }
          },
          5000,
        );
      }
    }, {
      immediate: true,
      deep: true,
    });

    return () => (
      <Drawer
        bodyStyle={{ padding: '24px', overflow: 'hidden' }}
        closable={false}
        destroyOnClose={true}
        mask={true}
        maskClosable={false}
        onClose={onClose}
        placement="right"
        title="本地处理冲突"
        v-model:open={open.value}
        width={924}
      >
        {{
          extra: () => (
            <Button
              class="flex items-center justify-center"
              icon={(
                <Icon class="font-size-18px" component={<Close />} />
              )}
              onClick={onClose}
              type="text"
            />
          ),
          default: () => (
            <div class="resolver-content h-full flex flex-col">
              <DrawerHeader cl={props.data?.cl} user={props.data?.submitter} />
              <div class="FO-Font-B16 mb-16px">
                处理选项
              </div>
              {workspaceOptions.value.length > 0
                ? (
                  <Form
                    layout="vertical"
                    model={form.value}
                    onFinish={onSubmit}
                    ref={formRef}
                    rules={{
                      workspace: [{ required: true, message: '请选择Workspace' }],
                    }}
                  >
                    <Form.Item label="选择 Workspace" name="workspace">
                      <Select allowClear placeholder="请选择 Workspace" v-model:value={form.value.workspace}>
                        {workspaceOptions.value.map((item) => (
                          <Select.Option key={item.value} value={item.value}>
                            <div class="h-full flex items-center gap-4px">
                              <Icon class="font-size-16px c-FO-Content-Icon2" component={<SystemFillWorkspace />} />
                              {item.label}
                            </div>
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <div>
                      {latestLocalResolver.value && renderShelveStatus()}
                    </div>
                  </Form>
                )
                : (
                  <div class="flex items-start gap-4px">
                    <Icon class="mt-2px font-size-18px c-FO-Functional-Error1-Default" component={<Subtract />} />
                    <div>
                      在当前用户
                      <span class="FO-Font-B14 mx-4px">{authStore.userProfile?.nickName}({authStore.userProfile?.userName})</span>
                      名下找不到绑定了目标分支 (
                      {currentRule.value?.targetStream}
                      ) 的 Workspace，请创建后再试。点击查看
                      <a
                        class="c-FO-Content-Link-Default"
                        href="https://hypergryph.feishu.cn/wiki/WjISwxuhBiLRK4k8wJhcSLAnnbg"
                        target="_blank"
                      >
                        详细教程
                      </a>
                    </div>
                  </div>
                )}
            </div>
          ),
          footer: () => (
            <div class="flex items-center justify-end gap-12px">
              <Button class="btn-fill-default" onClick={onClose} type="text">关闭</Button>
              {workspaceOptions.value.length > 0 && (
                <Button
                  class="btn-fill-primary"
                  disabled={latestLocalResolver.value?.status === MergeV1OperationStatus.OPERATION_STATUS_PROCESSING || !form.value.workspace}
                  onClick={onSubmit}
                  type="primary"
                >
                  创建 Shelve
                </Button>
              )}
            </div>
          ),
        }}
      </Drawer>
    );
  },
});

export {
  LocalResolver,
};
