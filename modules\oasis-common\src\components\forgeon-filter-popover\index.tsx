import { Popover, Tooltip } from 'ant-design-vue';
import type { TooltipPlacement } from 'ant-design-vue/es/tooltip';
import { type PropType, defineComponent, ref } from 'vue';

const ForgeonFilterPopover = defineComponent({
  props: {
    label: {
      type: String as PropType<string>,
      default: '筛选',
    },
    trigger: {
      type: String as PropType<'click' | 'hover'>,
      default: 'click',
    },
    placement: {
      type: String as PropType<TooltipPlacement>,
      default: 'bottom',
    },
    tooltip: {
      type: [Boolean, Object] as PropType<boolean | {
        title: string;
        placement: TooltipPlacement;
      }>,
      default: false,
    },
    tooltipContent: {
      type: String as PropType<string>,
      default: '',
    },
    onSubmit: {
      type: Function as PropType<() => void | Promise<void>>,
      default: () => {},
    },
  },
  setup(props, { slots }) {
    const isVisible = ref(false);

    const onVisibleChange = async (visible: boolean) => {
      if (!visible) {
        // 当弹窗关闭时触发提交操作
        await props.onSubmit();
      }
      isVisible.value = visible;
    };

    return () => (
      <Popover
        class="filter-popover-wrapper"
        onOpenChange={onVisibleChange}
        open={isVisible.value}
        placement={props.placement}
        trigger={props.trigger}
      >
        {{
          default: () => (
            props.tooltip
              ? (
                <Tooltip
                  class="filter-popover-tooltip"
                  {...(typeof props.tooltip === 'object' ? props.tooltip : {})}
                >
                  {{
                    default: () => (slots.default ? slots.default() : <div class="filter-popover-trigger">{props.label}</div>),
                    title: () => (typeof props.tooltip === 'object' ? props.tooltip.title : props.tooltipContent),
                  }}
                </Tooltip>
              )
              : (slots.default ? slots.default() : <div class="filter-popover-trigger">{props.label}</div>)
          ),
          content: () => (
            <div class="filter-popover-content">
              {slots.title ? slots.title() : null}
              {slots.content ? slots.content() : null}
            </div>
          ),
        }}
      </Popover>
    );
  },
});

export { ForgeonFilterPopover };
