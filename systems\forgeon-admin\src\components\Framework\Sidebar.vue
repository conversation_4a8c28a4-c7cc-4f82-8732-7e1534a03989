<template>
  <div class="w-230px bg-FO-Container-Fill1">
    <ForgeOnMenu
      v-model:activeMenu="activeMenu"
      v-model:openKeys="openKeys"
      :menus="menuList"
      :onPathChange="handleChangeRoute"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, h } from 'vue';
import {
  type ICommonMenuItem,
  filterAuthedMenu,
  ForgeOnMenu,
  PlatformEnterPoint,
  PlatformRoutePath,
  useForgeOnActiveMenu,
  useMicroAppInject,
  usePermissionCtx,
} from '@hg-tech/oasis-common';
import { type RouteLocationRaw, useRouter } from 'vue-router';
import { PermissionPoint } from '../../constants/permission.ts';
import FillCardIcon from '../../assets/icons/fill-card.svg?component';
import FillProjectIcon from '../../assets/icons/fill-project.svg?component';

import { checkAdminPermission } from '../../services/permission.ts';

const router = useRouter();
const { data } = useMicroAppInject(usePermissionCtx);
const menuList = computed<ICommonMenuItem[]>(() => {
  return filterAuthedMenu([
    {
      title: '页面配置',
      key: PlatformEnterPoint.HomePageManagement,
      svgIcon: () => h(FillCardIcon, {}),
      children: [
        {
          title: '首页 Banner',
          key: PlatformEnterPoint.BannerManagement,
          path: PlatformRoutePath.BannerManagement,
          permissionDeclare: {
            any: [PermissionPoint.BannerManagement],
          },
        },
        {
          title: '产品动态',
          key: PlatformEnterPoint.ProductionNews,
          path: PlatformRoutePath.ProductionNews,
          permissionDeclare: {
            any: [PermissionPoint.ProductionNews],
          },
        },
      ],
    },
    {
      title: '项目配置',
      svgIcon: () => h(FillProjectIcon, {}),
      key: PlatformEnterPoint.ProjectSettings,
      path: PlatformRoutePath.ProjectSettings,
      permissionDeclare: {
        any: [PermissionPoint.ProjectSettings],
      },
    },
  ], (d) => checkAdminPermission(d, data.value?.permissionInfo));
});
const { activeMenu, openKeys } = useForgeOnActiveMenu(menuList, computed(() => router.currentRoute.value.path));

function handleChangeRoute(options: RouteLocationRaw, openInNewTab?: boolean) {
  const targetRoute = router.resolve(options);

  if (openInNewTab) {
    window.open(targetRoute.href, '_blank');
  } else {
    router.push(options);
  }
}
</script>
