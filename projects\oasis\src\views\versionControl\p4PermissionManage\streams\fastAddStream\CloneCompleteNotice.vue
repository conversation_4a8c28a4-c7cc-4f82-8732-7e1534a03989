<template>
  <div :class="prefixCls">
    <div class="my-15 w-full flex items-center justify-center">
      <div>
        <Icon icon="icon-park-outline:comment" :size="80" />
      </div>
      <div class="ml-6 w-400px">
        <div class="text-xl">
          从已存在分支中克隆提交完成通知:
        </div>
        <div class="mt-6 flex">
          <div class="mt-1">
            分支：
          </div>
          <BasicForm class="w-270px" @register="registerForm" />
        </div>
      </div>
    </div>
    <div class="w-full flex justify-center">
      <a-button type="primary" :loading="loading" @click="handleClone">
        克隆提交完成通知
      </a-button>
      <a-button class="ml-6" :loading="loading" @click="handleSkip">
        跳过
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { type PropType, onMounted, ref } from 'vue';
import { cloneCompleteNoticeFormSchema } from './fastAddStream.data';
import type { SwarmReviewProjectsListItem } from '/@/api/page/model/swarmModel';
import { copyP4CompleteNotice } from '/@/api/page/p4';
import { BasicForm, useForm } from '/@/components/Form/index';
import Icon from '/@/components/Icon';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStoreWithOut } from '/@/store/modules/user';
import type { StreamsListItem } from '/@/api/page/model/p4Model';

defineOptions({
  name: 'CloneCompleteNotice',
});

const props = defineProps({
  depotID: {
    type: Number,
    required: true,
  },
  streamID: {
    type: Number,
    required: true,
  },
  auditList: {
    type: Array as PropType<SwarmReviewProjectsListItem[]>,
    default: () => [],
  },
  hasAudit: {
    type: Boolean,
    default: false,
  },
  streamList: {
    type: Array as PropType<StreamsListItem[]>,
    default: () => [],
  },
});
const emit = defineEmits(['nextStep']);
const userStore = useUserStoreWithOut();
const { prefixCls } = useDesign('clone-complete-notice');
const loading = ref(false);

const [registerForm, { validate, updateSchema }] = useForm({
  schemas: cloneCompleteNoticeFormSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
});

async function handleClone() {
  try {
    const values = await validate();

    loading.value = true;
    if (!userStore.getProjectId) {
      return;
    }

    if (values.copyFromStreamID) {
      const res = await copyP4CompleteNotice(userStore.getProjectId, {
        fromStreamID: values.copyFromStreamID,
        toStreamID: props.streamID,
      });

      emit('nextStep', { status: res?.code === 7 ? 2 : 1 });
    }
  } finally {
    loading.value = false;
  }
}

function handleSkip() {
  emit('nextStep', { status: 0 });
}

onMounted(async () => {
  if (props.hasAudit) {
    handleSkip();
  }

  await updateSchema({
    field: 'copyFromStreamID',
    componentProps: {
      options: props.streamList?.filter((e: StreamsListItem) => e.hasNoticeRule),
      optionFilterProp: 'description',
    },
  });
});
</script>
