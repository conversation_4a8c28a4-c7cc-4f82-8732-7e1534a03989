<template>
  <PageContainer class="flex-auto overflow-auto">
    <div class="mb-[12px] flex items-center justify-between">
      <div class="font-bold">
        Banner 列表
      </div>
      <Button type="primary" @click="handleAddBanner">
        添加
      </Button>
    </div>
    <Table
      :rowKey="record => record.id"
      :data-source="renderTableData"
      :columns="tableColumn"
      :loading="loading"
      :pagination="paginationProp"
      @change="onTableChange"
    />
    <FormModalHolder />
  </PageContainer>
</template>

<script setup lang="tsx">
import { type TableProps, Button, message, Popconfirm, Switch, Table } from 'ant-design-vue';
import type { ArrayWrappedType } from '@hg-tech/utils';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import { forgeonAdminApi } from '../../../api';
import PageContainer from '../../../components/Framework/PageContainer.vue';
import { computed } from 'vue';
import BannerFormModal from './BannerFormModal.vue';
import type { BannerFormData } from './FormData.ts';
import { previewImage } from '../../../utils/image.ts';
import { useAntdTable } from '../../../composition/useAntdTable.ts';

const [FormModalHolder, show] = useModalShow(BannerFormModal);
const { data, loading, execute } = useLatestPromise(forgeonAdminApi.api.configServiceListConfigBanner);
const renderTableData = computed(() => data.value?.data?.data?.list ?? []);

const { onTableChange, refreshTableData, paginationProp } = useAntdTable(
  (pageInfo) => execute({ ...pageInfo }, {}).then((res) => {
    const data = res?.data?.data;
    if (data) {
      return {
        ...data,
        total: Number(data.total) || 0,
      };
    }
    return data;
  }),
  { immediate: true },
);

const tableColumn = computed<TableProps['columns']>(() => [
  {
    title: '标题',
    width: 250,
    dataIndex: 'title',
  },
  {
    title: '图片',
    width: 400,
    customRender({ record }) {
      return (
        <img
          alt={record.title}
          class="h-[50px] cursor-pointer"
          onClick={() => previewImage({
            images: [record.imagePreview!],
          })}
          src={record.imageThumbnail}
        />
      );
    },
  },
  {
    title: '跳转链接',
    width: 200,
    customRender({ record }) {
      return <a class="break-all" href={record.url} target="_blank">{record.url}</a>;
    },
  },
  {
    title: '排序',
    dataIndex: 'position',
    width: 120,
  },
  {
    title: '状态',
    align: 'center',
    width: 120,
    customRender({ record }) {
      return (
        <Switch
          checked={record.status === 'enable'}
          onChange={() => handleChangeStatus(
            record.status === 'enable' ? 'disable' : 'enable',
            record,
          )}
        />
      );
    },
  },
  {
    title: '操作',
    align: 'center',
    width: 180,
    customRender({ record }) {
      return (
        <div>
          <Button onClick={() => handleEditBanner(record)} type="link">编辑</Button>
          <Popconfirm
            ok-text="删除"
            okButtonProps={{ danger: true }}
            onConfirm={() => handleDeleteBanner(record)}
            title="确认要删除吗？"
          >
            <Button danger type="link">删除</Button>
          </Popconfirm>
        </div>
      );
    },
  },
]);

async function handleChangeStatus(status: BannerFormData['status'], row: ArrayWrappedType<typeof renderTableData.value>) {
  await forgeonAdminApi.api.configServiceUpdateConfigBanner({ id: String(row.id) }, {
    status,
    title: row.title,
    image: row.image,
    url: row.url,
    position: row.position,
  });
  message.success('操作成功');
  return refreshTableData();
}
async function handleAddBanner() {
  await show({
    title: '添加 Banner',
    sentReq(formValue) {
      return forgeonAdminApi.api.configServiceCreateConfigBanner({}, formValue);
    },
  });
  message.success('添加成功');
  return refreshTableData();
}
async function handleEditBanner(row: ArrayWrappedType<typeof renderTableData.value>) {
  await show({
    title: '编辑 Banner',
    imagePreviewMap: { [row.image!]: row.imagePreview! },
    initData: {
      title: row.title,
      image: row.image,
      url: row.url,
      position: row.position,
      status: row.status as BannerFormData['status'],
    },
    sentReq(formValue) {
      return forgeonAdminApi.api.configServiceUpdateConfigBanner({ id: String(row.id) }, formValue);
    },
  });
  message.success('编辑成功');
  return refreshTableData();
}
async function handleDeleteBanner(row: ArrayWrappedType<typeof renderTableData.value>) {
  await forgeonAdminApi.api.configServiceDeleteConfigBanner({ id: String(row.id) }, {});
  message.success('删除成功');
  return refreshTableData();
}
</script>
