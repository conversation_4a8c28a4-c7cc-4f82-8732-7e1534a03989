<template>
  <BasicModal
    v-bind="$attrs"

    title="克隆其他分支的Swarm配置"
    width="700px"
    destroyonclose showfooter
    :maskClosable="false"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { pick } from 'lodash-es';
import { ref, unref } from 'vue';
import { cloneFormSchema } from '../swarmSettings.data.tsx';
import type { SwarmReviewProjectsListItem } from '../../../../api/page/model/swarmModel';
import { addSwarmReviewProject } from '../../../../api/page/swarm';
import { BasicForm, useForm } from '../../../../components/Form';
import { type ModalMethods, BasicModal, useModalInner } from '../../../../components/Modal';
import { useUserStoreWithOut } from '../../../../store/modules/user';

const emit = defineEmits<{
  success: [id: number, copyFromReviewProjectID: number];
  register: [modalMethod: ModalMethods, uuid: number];
}>();

const userStore = useUserStoreWithOut();
const streamID = ref();
const reviewProjectList = ref<SwarmReviewProjectsListItem[]>([]);

const [registerForm, { resetFields, validate, updateSchema }] = useForm({
  labelWidth: 120,
  schemas: cloneFormSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 22 },
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });
  await resetFields();
  streamID.value = data?.stream?.ID;
  reviewProjectList.value = data?.reviewProjectList;
  await updateSchema({
    field: 'copyFromReviewProjectID',
    componentProps: {
      options: reviewProjectList.value?.filter((e) => e.stream?.streamType !== 4).map((e) => ({
        label: e.stream?.description || e.stream?.path,
        value: e.ID,
      })),
      optionFilterProp: 'label',
    },
  });
  setModalProps({ confirmLoading: false });
});

async function handleSubmit() {
  try {
    const values = await validate();

    setModalProps({ confirmLoading: true });

    if (!userStore.getProjectId) {
      return;
    }

    const originProject = reviewProjectList.value.find(
      (item) => item.ID === values.copyFromReviewProjectID,
    );
    const submitData = Object.assign(
      { streamID: unref(streamID), name: values.name, description: values.name },
      pick(originProject, ['members', 'subGroups']),
    );

    const { id } = await addSwarmReviewProject(userStore.getProjectId, submitData);

    closeModal();
    emit('success', id, values.copyFromReviewProjectID);
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
