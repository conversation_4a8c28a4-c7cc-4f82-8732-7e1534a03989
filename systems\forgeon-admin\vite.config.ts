import { defineViteConfigProjectVueSys } from '@hg-tech/configs';
import UnoCSS from 'unocss/vite';
import process from 'node:process';
import svgLoader from 'vite-svg-loader';

// https://vitejs.dev/config/
export default defineViteConfigProjectVueSys(() => {
  return {
    plugins: [
      UnoCSS(),
      svgLoader(),
    ],
    server: {
      host: true,
      port: 3102,
    },
  };
}, {
  analyze: process.argv.includes('--analyze'),
});
