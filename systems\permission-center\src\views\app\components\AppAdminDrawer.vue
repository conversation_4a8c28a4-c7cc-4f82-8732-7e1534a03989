<template>
  <Drawer
    title="项目管理员配置" placement="right" :open="show" :width="924" :closable="false"
    :bodyStyle="{ display: 'flex', flexDirection: 'column', height: '100%' }" :maskClosable="false"
    @afterOpenChange="o => !o && modalDestroy()" @close="() => modalConfirm()"
  >
    <template #extra>
      <Button class="btn-fill-text" @click="() => modalCancel()">
        <template #icon>
          <CloseIcon />
        </template>
      </Button>
    </template>
    <Alert message="项目管理员可以管理应用内的项目角色，配置角色的权限和成员" type="info" class="mb-[16px] b-none" showIcon />
    <div ref="tableWrapperRef" class="min-h-0 flex-1">
      <Table
        :loading="loadingMemberList" :columns="columns" :data-source="tenantList" :pagination="false"
        :scroll="{ y: dynamicScrollY }" class="app-admin-drawer-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'members'">
            <div v-if="editingAppTenantId === record.id">
              <Select
                v-model:value="editMemberHgIds" size="small" mode="multiple" class="w-full"
                placeholder="请输入用户姓名/昵称/邮箱/拼音" :loading="loadingUserList" :options="userListOptions"
                :filterOption="false" allowClear showArrow :maxTagCount="3" @focus="resetUserList"
                @search="handleSearch"
              />
            </div>
            <div v-else class="flex flex-wrap gap-4px">
              <Tag v-for="member in record.members" :key="member" :bordered="false" class="m-ie-0 bg-FO-Container-Fill4">
                {{ member.name }}
                <span v-if="member.nickname">({{ member.nickname }})</span>
              </Tag>
              <template v-if="!record.members?.length">
                无
              </template>
            </div>
          </template>
          <template v-else-if="column.key === 'action'">
            <div>
              <div v-if="editingAppTenantId === record.id" class="flex items-center gap-2">
                <Button class="btn-fill-text" @click="() => cancel()">
                  <template #icon>
                    <CloseIcon />
                  </template>
                </Button>
                <Button class="btn-fill-text" @click="() => save()">
                  <template #icon>
                    <CheckIcon />
                  </template>
                </Button>
              </div>
              <div v-else>
                <Tooltip title="编辑">
                  <Button
                    :disabled="!!editingAppTenantId && editingAppTenantId !== record.id"
                    class="btn-fill-text" @click="edit(record.id)"
                  >
                    <template #icon>
                      <EditIcon />
                    </template>
                  </Button>
                </Tooltip>
              </div>
            </div>
          </template>
        </template>
      </Table>
    </div>
  </Drawer>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Alert, Button, Drawer, message, Select, Table, Tag, Tooltip } from 'ant-design-vue';
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import { useDebounceFn, useResizeObserver } from '@vueuse/core';
import { type PermissionAppInfo, getPermissionTenantsMembers, updatePermissionTenantMembers } from '../../../api/app.ts';
import type { SysUserInfo } from '../../../api/users.ts';
import { useUserListOption } from '../../../composables/useUserInfo.ts';
import CloseIcon from '../../../assets/icons/fill-close.svg?component';
import CheckIcon from '../../../assets/icons/fill-check.svg?component';
import EditIcon from '../../../assets/icons/fill-edit.svg?component';

const props = defineProps<ModalBaseProps & {
  appId?: PermissionAppInfo['id'];
}>();

const editMembers = ref<SysUserInfo[]>([]);
const editMemberHgIds = ref<string[]>([]);
const { userListOptions, loadingUserList, queryUser, resetUserList } = useUserListOption(computed(() => editMembers.value || []));
const { data: tenantMemberList, execute: fetchTenantMemberList, loading: loadingMemberList } = useLatestPromise(getPermissionTenantsMembers);
const tableWrapperRef = ref<HTMLElement | null>(null);
const dynamicScrollY = ref(0);

const tenantList = computed(() => tenantMemberList.value?.data?.data ?? []);

useResizeObserver(tableWrapperRef, (entries) => {
  const entry = entries[0];
  if (entry) {
    const { height } = entry.contentRect;
    const tableHeader = document.querySelector('.app-admin-drawer-table .ant-table-thead');
    const TABLE_HEADER_HEIGHT = tableHeader?.clientHeight || 55;
    dynamicScrollY.value = Math.max(200, height - TABLE_HEADER_HEIGHT);
  }
});

const handleSearch = useDebounceFn((v: string) => {
  if (v) {
    queryUser({ query: v }, {});
  } else {
    resetUserList();
  }
}, 300);

const columns = [
  {
    title: '项目',
    dataIndex: 'tenant',
    width: '30%',
  },
  {
    title: '项目管理员',
    dataIndex: 'members',
  },
  {
    title: '操作',
    key: 'action',
    width: '10%',
  },
];

const editingAppTenantId = ref<number>();

function edit(id: number) {
  editMembers.value = tenantList.value.find((i) => i.id === id)?.members || [];
  editMemberHgIds.value = editMembers.value.map((i) => i.hgId!);
  resetUserList();
  editingAppTenantId.value = id;
}

async function save() {
  if (!editingAppTenantId.value) {
    message.warning('项目id不存在');
    return;
  }
  try {
    const res = await updatePermissionTenantMembers({ appId: props.appId, appTenantId: editingAppTenantId.value }, { member: editMemberHgIds.value });
    if (res.data?.code === 0) {
      message.success('保存成功');
      editingAppTenantId.value = undefined;
      await fetchTenantMemberList({ appId: props.appId }, {});
    }
  } catch (error) {
    message.error(`保存失败：${(error as Error).message}`);
    console.error(error);
  }
}

function cancel() {
  editMembers.value = [];
  editMemberHgIds.value = [];
  editingAppTenantId.value = undefined;
}

watch(() => props.appId, (newVal) => {
  if (newVal) {
    fetchTenantMemberList({ appId: props.appId }, {});
  }
}, { immediate: true });
</script>
