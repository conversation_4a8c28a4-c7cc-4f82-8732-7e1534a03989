import type { PermissionAppInfo, PermissionTenantInfo } from './app';
import type { PermissionCheckPoint } from './manage.ts';
import type { SysLdapGroupListItem, SysUserInfo } from './users.ts';
import type { PermissionBaseRes } from './_common.ts';
import { apiService } from '../services/req.ts';

export enum OrgStructureType {
  Member = 'member',
  Department = 'org',
  CustomGroup = 'group',
}

export enum LdapGroupCategory {
  Department = '0',
  CustomGroup = '1',
}

export interface PermissionMember {
  member?: SysUserInfo[];
  pmp?: SysLdapGroupListItem[];
}

export interface PermissionGroupMemberItem {
  id: number;
  createdAt: string;
  name: string;
  type: OrgStructureType;
  autoRemoveTime?: string;
  resourceId: string;
}

export interface PermissionMemberIds {
  member?: string[];
  pmp?: string[];
}

export interface PermissionAppGroupListItem {
  id?: number;
  appId?: PermissionAppInfo['id'];
  createdAt?: string;
  updatedAt?: string;
  name?: string;
  creator?: string;
  isPublic?: boolean;
}

export interface PermissionAppGroupDetail extends PermissionAppGroupListItem {
  members?: PermissionMember;
  resources?: PermissionCheckPoint['id'][];
}

// 新的权限组详情接口，匹配后端返回结构
export interface PermissionAppGroupDetailNew extends PermissionAppGroupListItem {
  members?: PermissionGroupMemberItem[];
  resources?: PermissionCheckPoint['id'][];
  memberCnt?: number;
  orgCnt?: number;
  groupCnt?: number;
}

/**
 * 获取应用权限组列表
 */
export const getPermissionAppGroups = apiService.GET<
  { appId: PermissionAppInfo['id'];tenantId?: PermissionTenantInfo['id']; search?: string },
  Record<string, never>,
  PermissionBaseRes<PermissionAppGroupListItem[]>
>(`/api/auth/v1/app/{appId}/groups`);

export interface PermissionGroupForm {
  name: string;
  isPublic?: boolean;
}

/**
 * 创建权限组
 */
export const createPermissionGroup = apiService.POST<
  { appId: PermissionAppInfo['id']; tenantId?: PermissionTenantInfo['id'] },
  PermissionGroupForm,
  PermissionBaseRes<PermissionAppGroupDetail>
>(`/api/auth/v1/app/{appId}/group`);

/**
 * 权限组详情
 */
export const fetchPermissionGroupDetail = apiService.GET<
  { appId: PermissionAppInfo['id']; groupId: PermissionAppGroupDetail['id']; tenantId?: PermissionTenantInfo['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionAppGroupDetailNew>
>(`/api/auth/v1/app/{appId}/group/{groupId}`);

/**
 * 权限组详情
 */
export const fetchPermissionGroupDetailV2 = apiService.GET<
  { appId: PermissionAppInfo['id']; groupId: PermissionAppGroupDetail['id']; tenantId?: PermissionTenantInfo['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionAppGroupDetailNew>
>(`/api/auth/v2/app/{appId}/group/{groupId}`);

/**
 * 更新权限组
 */
export const updatePermissionGroup = apiService.PUT<
  { appId: PermissionAppInfo['id']; groupId: PermissionAppGroupDetail['id']; tenantId?: PermissionTenantInfo['id'] },
  Partial<PermissionGroupForm>,
  PermissionBaseRes<PermissionAppGroupDetail>
>(`/api/auth/v1/app/{appId}/group/{groupId}`);

/**
 * 更新权限组成员
 */
export const updatePermissionGroupMembers = apiService.PUT<
  { appId: PermissionAppInfo['id']; groupId: PermissionAppGroupDetail['id']; tenantId?: PermissionTenantInfo['id'] },
  PermissionMemberIds,
  PermissionBaseRes<PermissionAppGroupDetail>
>(`/api/auth/v1/app/{appId}/group/{groupId}/members`);

/**
 * 更新权限组权限
 */
export const updatePermissionGroupResource = apiService.PUT<
  { appId: PermissionAppInfo['id']; groupId: PermissionAppGroupDetail['id']; tenantId?: PermissionTenantInfo['id'] },
  NonNullable<PermissionAppGroupDetail['resources']>,
  PermissionBaseRes<PermissionAppGroupDetail>
>(`/api/auth/v1/app/{appId}/group/{groupId}/resource`);

/**
 * 删除权限组
 */
export const deletePermissionGroup = apiService.DELETE<
  { appId: PermissionAppInfo['id']; groupId: PermissionAppGroupDetail['id']; tenantId?: PermissionTenantInfo['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionAppGroupDetail>
>(`/api/auth/v1/app/{appId}/group/{groupId}`);

/**
 * 复制权限组
 */
export const copyPermissionGroup = apiService.POST<
  { appId: PermissionAppInfo['id']; tenantId?: PermissionTenantInfo['id'] },
  {
    name: string;
    fromGroupId?: number;
  },
  PermissionBaseRes<PermissionAppGroupDetail>
>(`/api/auth/v1/app/{appId}/group/copy`);

/**
 * 更新权限组排序
 */
export const updatePermissionGroupOrder = apiService.PUT<
  { appId: PermissionAppInfo['id']; tenantId?: PermissionTenantInfo['id']; search?: string },
  {
    order: number[];
  },
  PermissionBaseRes<PermissionAppGroupDetail>
>(`/api/auth/v1/app/{appId}/groups/order`);

/**
 * 添加成员
 */
export const addMemberToGroup = apiService.PUT<
  { appId: PermissionAppInfo['id']; groupId: PermissionAppGroupDetail['id']; tenantId?: PermissionTenantInfo['id'] },
  {
    /**
     * member - 用户  org - 组织架构  group - 群组
     */
    type?: OrgStructureType;
    /**
     * 相应id
     */
    resourceId?: string;
    /**
     * 自动移除时间  时间戳（秒）
     */
    autoRemoveTime?: number;
  }[],
  PermissionBaseRes<PermissionAppGroupDetail>
>(`/api/auth/v2/app/{appId}/group/{groupId}/add_member`);

/**
 * 删除成员
 */
export const removeMemberFromGroup = apiService.DELETE<
  { appId: PermissionAppInfo['id']; groupId: PermissionAppGroupDetail['id']; tenantId?: PermissionTenantInfo['id']; search?: string },
  {
    ids: number[];
  },
  PermissionBaseRes<PermissionAppGroupDetail>
>(`/api/auth/v2/app/{appId}/group/{groupId}/member`);

/**
 * 搜索权限组成员
 */
export const searchPermissionGroupMembers = apiService.GET<
  { appId: PermissionAppInfo['id']; groupId: PermissionAppGroupDetail['id']; search?: string; tenantId?: PermissionTenantInfo['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionGroupMemberItem[]>
>(`/api/auth/v2/app/{appId}/group/{groupId}/search`);

/**
 * 设置公共角色
 */
export const setPublicGroup = apiService.PUT<
  { appId: PermissionAppInfo['id']; groupId: PermissionAppGroupDetail['id']; tenantId?: PermissionTenantInfo['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionAppGroupDetail>
>(`/api/auth/v2/app/{appId}/group/{groupId}/set_public_group`);

/**
 * 取消公共角色
 */
export const cancelPublicGroup = apiService.PUT<
  { appId: PermissionAppInfo['id']; groupId: PermissionAppGroupDetail['id']; tenantId?: PermissionTenantInfo['id'] },
  { isDelete?: boolean },
  PermissionBaseRes<PermissionAppGroupDetail>
>(`/api/auth/v2/app/{appId}/group/{groupId}/cancel_public_group`);
