import type { UserInfoModel } from './../../sys/model/userModel';
import type { BaseItem, BasicFetchResult, BasicPageParams } from '/@/api/model/baseModel';
import type { RegexCustomPath, StreamsListItem, UpdateP4PermissionItem } from '/@/api/page/model/p4Model';

// Swarm工作流列表 Model
export interface SwarmWorkflowsListItem {
  id: number;
  name: string;
  description: string;
}

// Swarm工作流列表 接口参数
export type SwarmWorkflowsPageParams = BasicPageParams & { type?: number; serverID?: number };

// Swarm工作流列表 接口返回数据
export type SwarmWorkflowsListGetResultModel = BasicFetchResult<SwarmWorkflowsListItem>;

// Swarm审查项目列表 Model
export interface SwarmReviewProjectsListItem extends BaseItem {
  swarmProjectID?: string;
  swarmProjectName?: string;
  stream?: StreamsListItem;
  streamID?: number;
  members?: string[];
  subGroups?: string[];
  chatID?: string;
}

// 更新提交关注群聊 接口参数
export interface EditP4ConcernProjectChatParams {
  streamID: number;
  chatID: string;
}

// Swarm审查项目列表 接口参数
export type SwarmReviewProjectsPageParams = BasicPageParams & { depotID: number };

// Swarm审查项目列表 接口返回数据
export type SwarmReviewProjectsListGetResultModel = BasicFetchResult<SwarmReviewProjectsListItem>;

export enum SwarmGroupLockStatus {
  Closed = 0,
  Locked = 3,
}

// Swarm审查组列表 Model
export interface SwarmGroupListItem extends BaseItem {
  name?: string;
  projectID?: number;
  workFlowID?: number;
  reviewProjectID?: number;
  swarmBranchID?: string;
  swarmBranchName?: string;
  defaultReviewers?: SwarmAuditorListItem[];
  moderatorReviewers?: SwarmAuditorListItem[];
  defaultReviewerIDs?: number[];
  defaultRequireIDs?: number[];
  defaultReviewerGroups?: string[];
  isModerators?: boolean;
  lockStatus?: SwarmGroupLockStatus;
  lockGroup?: GroupLockListItem;
  UUID?: number;
  isReviewers?: boolean;
  isValid?: boolean;
  reviewPaths: UpdateP4PermissionItem[];
  customizeReviewPaths?: RegexCustomPath[];
  timeoutConfig?: {
    /**
     * 超时时间，秒级时间戳
     */
    timeLimit?: number;
    additionalNotifier?: {
      specialGroups?: string[];
      members?: number[];
    };
    additionalNotifyChatGroupIDs?: string[];
  };
}

export interface SwarmGroupItemInfo {
  swarmReviewGroup?: {
    ID?: number;
    projectID?: number;
    streamID?: number;
    /**
     * 关注项目id
     */
    concernProjectID?: number;
    /**
     * 审查项目id
     */
    reviewProjectID?: number;
    /**
     * 关注组id
     */
    concernGroupID?: number;
    /**
     * 审查组id
     */
    reviewGroupID?: number;
  };
  /**
   * 原有reviewGroup结构体不变
   */
  reviewGroup?: SwarmGroupListItem;
  concernGroup?: {
    manualUsers?: UserInfoModel[];
    p4Groups?: string[];
    mustReviewConcernSubmitOption?: boolean;
    isValid?: boolean;
  };
}

// Swarm审查干员列表 Model
export interface SwarmAuditorListItem {
  ID?: number;
  requiredApprove?: boolean;
  reviewGroupId?: number;
  sysUserId: number;
  user?: UserInfoModel;
}

// 复制P4分支Swarm  接口参数
export interface CopyP4StreamSwarmParams {
  fromReviewProjectID: number;
  toReviewProjectID: number;
}

// 复制P4分支关注  接口参数
export interface CopyP4StreamConcernParams {
  fromStreamID: number;
  toStreamID: number;
}

// Swarm审查项目通知列表 Model
export interface SwarmReviewMessagesListItem extends BaseItem {
  projectID?: number;
  streamID?: number;
  chatID?: number;
}

// Swarm审查项目列表 接口参数
export type SwarmReviewMessagesPageParams = SwarmReviewMessagesListItem & BasicPageParams;

// Swarm审查项目列表 接口返回数据
export type SwarmReviewMessagesListGetResultModel = BasicFetchResult<SwarmReviewMessagesListItem>;

// Swarm全局工作流列表 Model
export interface SwarmWorkflowGlobalsItem extends BaseItem {
  ignoreGroups?: string[];
  ignoreUsers?: string[];
  projectID?: number;
  serverID?: number;
}

// Swarm全局工作流 接口返回数据
export interface SwarmWorkflowGlobalsGetResultModel {
  globalWorkflow: SwarmWorkflowGlobalsItem;
}

// 审查组锁列表 Model
export interface GroupLockListItem extends BaseItem {
  groupID?: number;
  qaIds?: number[];
  chatID?: string;
  isOnlyQa?: boolean;
  isChat?: boolean;
  chatName?: string;
  beforeWorkflow?: number;
  timeoutConfig?: {
    timeLimit?: number;
    additionalNotifier?: {
      specialGroups?: string[];
      members?: number[];
    };
    additionalNotifyChatGroupIDs?: string[];
    permissionProcess?: {
      specialGroups?: string[];
      members?: number[];
    };
  };
}

export interface P4StreamLockConfigReviewerItem extends BaseItem {
  lockReviewerIds?: number[];
  lockReviewerGroups?: string[];
  streamID: number;
  lockChatID?: string;
  lockReviewers?: UserInfoModel[];
}
export type P4StreamLockConfigReviewerParams = BasicPageParams & P4StreamLockConfigReviewerItem;

// Swarm审查项目列表 接口返回数据
export type P4StreamLockConfigReviewerGetResultModel =
  BasicFetchResult<P4StreamLockConfigReviewerItem>;

// 通知加急列表 Model
export interface ReviewUrgentListItem extends BaseItem {
  streamID: number;
  urgentLock: boolean;
  urgentReview: boolean;
}
export interface SwarmReviewGroupsConcernParams {
  streamID?: number;
  concernMemberIDs: number[];
  concernMemberGroups: string[];
  mustReviewConcernSubmitOption: boolean;
  swarmReviewGroupID?: number;
}
// 通知加急列表 接口参数
export type ReviewUrgentPageParams = BasicPageParams & Partial<ReviewUrgentListItem>;

// 通知加急列表 接口返回数据
export type ReviewUrgentListGetResultModel = BasicFetchResult<ReviewUrgentListItem>;
