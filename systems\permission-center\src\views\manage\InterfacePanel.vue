<template>
  <div class="h-full w-full flex flex-col">
    <div class="mb-16px flex justify-between">
      <div />
      <div class="flex gap-12px">
        <Input v-model:value.trim="inputSearchKw" allowClear placeholder="搜索接口">
          <template #prefix>
            <SearchIcon class="c-FO-Content-Icon1" />
          </template>
        </Input>
        <Button class="btn-fill-primary" @click="() => handleNewApi()">
          <template #icon>
            <AddIcon />
          </template>
          新增接口
        </Button>
      </div>
    </div>
    <div class="flex-1 overflow-hidden">
      <BasicVxeTable :options="gridOptions" />
    </div>
    <ApiModalHolder />
  </div>
</template>

<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { Button, Input, message, Modal, Tooltip } from 'ant-design-vue';
import { toHighlightSegments } from '@hg-tech/utils';
import { match } from 'pinyin-pro';
import { useHighlight } from '../../composables/useHighlight';
import { refDebounced } from '@vueuse/core';
import type { VxeGridProps } from 'vxe-table';
import { BasicVxeTable } from '@hg-tech/oasis-common';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import AddIcon from '../../assets/icons/fill-add.svg?component';
import EditIcon from '../../assets/icons/fill-edit.svg?component';
import DeleteIcon from '../../assets/icons/fill-delete.svg?component';
import WarningErrorIcon from '../../assets/icons/fill-warning-error.svg?component';
import SearchIcon from '../../assets/icons/fill-search.svg?component';
import {
  type PermissionCheckPointApi,
  createPermissionCheckPointApi,
  deletePermissionApi,
  fetchPermissionCheckPointApis,
  updatePermissionCheckPointApi,
} from '../../api/manage.ts';
import type { PermissionAppInfo } from '../../api/app.ts';
import ApiFormModal from './components/ApiFormModal.vue';
import ApiMethodTag from './components/ApiMethodTag.vue';

const props = defineProps<{
  appId: PermissionAppInfo['id'];
}>();

const { data: apiListRes, execute: fetchApiList } = useLatestPromise(fetchPermissionCheckPointApis);
const apiList = computed(() => apiListRes.value?.data?.data ?? []);
watch(() => props.appId, refreshTableData, { immediate: true });

async function refreshTableData() {
  if (props.appId) {
    return fetchApiList({ appId: props.appId }, {});
  }
}

interface RenderRow {
  id: number;
  name: string;
  method: string;
  path: string;
  item: PermissionCheckPointApi;
}

const inputSearchKw = ref<string>('');
const searchKw = refDebounced(inputSearchKw, 200);

const tableRows = computed<RenderRow[]>(() => {
  const filteredApis = searchKw.value
    ? apiList.value.filter((i) => {
      const kw = searchKw.value.toLowerCase();
      const name = i.name?.toLowerCase() || '';
      const path = i.path?.toLowerCase() || '';
      const method = i.method?.toLowerCase() || '';

      return name.includes(kw) // 匹配接口名称
        || path.includes(kw) // 匹配接口路径
        || method.includes(kw) // 匹配请求方法
        || match(name, kw)?.length; // 匹配拼音
    })
    : [...apiList.value];

  return filteredApis.map((api) => ({
    id: api.id!,
    name: api.name || '',
    method: api.method || '',
    path: api.path || '',
    item: api,
  }));
});

const { getHighlightSegments } = useHighlight();

const tableColumns = computed<VxeGridProps<RenderRow>['columns']>(() => [
  {
    field: 'name',
    title: '接口名称',
    minWidth: 200,
    slots: {
      default({ row }) {
        return searchKw.value
          ? getHighlightSegments(row.name, searchKw.value).map((i) => <span class={i.highlight ? 'c-FO-Brand-Primary-Default' : ''}>{i.text}</span>)
          : row.name;
      },
    },
  },
  {
    field: 'method',
    title: '请求方法',
    width: 100,
    slots: {
      default({ row }) {
        return <ApiMethodTag method={row.method} />;
      },
    },
  },
  {
    field: 'path',
    title: 'API路径',
    minWidth: 250,
    slots: {
      default({ row }) {
        return searchKw.value
          ? toHighlightSegments(row.path, searchKw.value).map((i) => <span class={`FO-Font-R14 ${i.highlight ? 'c-FO-Brand-Primary-Default' : ''}`}>{i.text}</span>)
          : <span class="FO-Font-R14">{row.path}</span>;
      },
    },
  },
  {
    field: 'actions',
    title: '操作',
    width: 120,
    slots: {
      default({ row }) {
        return (
          <div class="flex items-center gap-4px">
            <Tooltip title="编辑接口">
              <Button class="btn-fill-text" onClick={() => handleEditApi(row)}>
                {{ icon: () => <EditIcon class="text-16px" /> }}
              </Button>
            </Tooltip>
            <Tooltip title="删除接口">
              <Button class="btn-fill-text" onClick={() => handleDeleteApi(row)}>
                {{ icon: () => <DeleteIcon class="text-16px" /> }}
              </Button>
            </Tooltip>
          </div>
        );
      },
    },
  },
] as VxeGridProps<RenderRow>['columns']);

const gridOptions = computed(() => ({
  rowConfig: {
    keyField: 'id',
    isHover: true,
  },
  height: 'auto',
  columns: tableColumns.value,
  data: tableRows.value,
  columnConfig: {
    resizable: false,
  },
}) as VxeGridProps<RenderRow>);

const [ApiModalHolder, showCreateApiModal] = useModalShow(ApiFormModal);

async function handleNewApi() {
  const result = await showCreateApiModal({
    title: '新增接口',
    async sentReq(formValue) {
      const res = await createPermissionCheckPointApi({ appId: props.appId }, formValue);
      return res.data?.code === 0 ? res.data?.data : undefined;
    },
  });
  if (result?.updatedItem) {
    message.success('保存成功');
    inputSearchKw.value = '';
    await refreshTableData();
    if (result.createAnother) {
      handleNewApi();
    }
  }
}

async function handleEditApi(row: RenderRow) {
  const result = await showCreateApiModal({
    title: '编辑接口',
    initData: row.item,
    async sentReq(formValue) {
      const res = await updatePermissionCheckPointApi({ appId: props.appId, apiId: row.item.id }, formValue);
      return res.data?.code === 0 ? res.data?.data : undefined;
    },
  });

  if (result?.updatedItem) {
    message.success('保存成功');
    await refreshTableData();
  }
}

async function handleDeleteApi(row: RenderRow) {
  Modal.confirm({
    icon: null,
    width: 496,
    okText: '删除',
    okButtonProps: {
      type: 'primary',
      danger: true,
    },
    cancelButtonProps: {
      // @ts-expect-error cancelButtonProps支持class但没有类型定义
      class: 'btn-fill-default',
    },
    centered: true,
    closable: true,
    title: () => {
      return (
        <div class="flex items-center">
          <WarningErrorIcon class="c-FO-Functional-Error1-Default" />
          <div class="F0-Font-B16 ml-8px">删除接口</div>
        </div>
      );
    },
    content() {
      return (
        <div class="mt-12px pb-8px c-FO-Content-Text2">
          删除接口【{row.item?.name}】，配置过此接口的权限会移除对应接口配置项，此操作不可恢复，请谨慎操作。
        </div>
      );
    },
    async onOk() {
      try {
        const res = await deletePermissionApi({ appId: props.appId, apiId: row.item.id }, {});
        if (res.data?.code === 0) {
          await refreshTableData();
          message.success('操作成功');
        }
      } catch (error) {
        console.error(error);
      }
    },
  });
}
</script>

<style lang="less" scoped>
:deep(.vxe-table--footer-inner-wrapper) {
  @apply !b-none;
}
</style>
