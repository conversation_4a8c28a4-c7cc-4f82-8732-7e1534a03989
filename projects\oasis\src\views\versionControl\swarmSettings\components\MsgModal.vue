<template>
  <BasicModal
    :wrapClassName="prefixCls"
    width="700px"
    okText="保存"
    destroyOnClose
    :maskClosable="false"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <template #title>
      <div class="w-full flex justify-center">
        配置{{ getName }}通知总群
      </div>
    </template>
    <div class="mx-auto w-560px text-lg">
      <div class="my-4">
        <template v-if="isLockGroup">
          <div>
            配置审批通知总群后，该分支所有<b>审批</b>都会额外发送至群聊
          </div>
          <div>
            若不配置群聊则只将审批通知发送至<b>审批人</b>
          </div>
        </template>
        <template v-else>
          <div>
            配置Review通知总群后，该分支所有<b>Review</b>都会额外发送至群聊
          </div>
          <div>
            若不配置群聊则只将审查通知发送至<b>Reviewers</b>
          </div>
        </template>
      </div>
      <BasicForm @register="registerForm">
        <template #chatID="{ model, field }">
          <div class="flex items-center">
            <div class="w-0 flex-1">
              <a-select
                v-model:value="model[field]"
                :options="isLockGroup ? chatList : swarmChatList"
                :placeholder="`请选择${getName}通知总群`"
                :fieldNames="{
                  label: 'name',
                  value: 'chat_id',
                }"
              />
            </div>
            <div class="ml-3">
              <a-button
                size="small"
                shape="round"
                type="primary"
                danger
                @click="model[field] = undefined"
              >
                清空群聊
              </a-button>
            </div>
          </div>
        </template>
      </BasicForm>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { msgFormSchema } from '../swarmSettings.data.tsx';
import type { P4StreamLockConfigReviewerItem, SwarmReviewMessagesListItem } from '../../../../api/page/model/swarmModel.ts';
import type { FeishuChatListItem } from '../../../../api/page/model/systemModel.ts';
import { addSwarmReviewMessage, editSwarmReviewMessage, setP4StreamLockConfigChat } from '../../../../api/page/swarm.ts';
import { BasicForm, useForm } from '../../../../components/Form/index.ts';
import { type ModalMethods, BasicModal, useModalInner } from '../../../../components/Modal/index.ts';
import { useDesign } from '../../../../hooks/web/useDesign.ts';
import { useUserStoreWithOut } from '../../../../store/modules/user.ts';

const emit = defineEmits<{
  (event: 'success'): void;
  (event: 'register', modalMethod: ModalMethods, uuid: number): void;
}>();

const { prefixCls } = useDesign('swarm-audit-project-msg-modal');
const userStore = useUserStoreWithOut();
const streamID = ref();
const messages = ref<SwarmReviewMessagesListItem[]>([]);
const chatList = ref<FeishuChatListItem[]>([]);
const swarmChatList = ref<FeishuChatListItem[]>([]);
const isUpdate = ref(false);
const isLockGroup = ref(false);
const lockConfig = ref<P4StreamLockConfigReviewerItem>();

const getName = computed(() => {
  return isLockGroup.value ? '审批' : 'Review';
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 560,
  schemas: [],
  showActionButtonGroup: false,
  layout: 'vertical',
  baseColProps: { span: 23 },
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });
  await resetFields();
  streamID.value = data?.stream?.ID;
  messages.value = data?.messages;
  isUpdate.value = data?.messages?.length > 0;
  isLockGroup.value = !!data?.isLockGroup;
  lockConfig.value = data?.lockConfig;
  chatList.value = data?.chatList || [];
  swarmChatList.value = data?.swarmChatList || [];

  await setProps({
    schemas: msgFormSchema(getName.value, isLockGroup.value ? '提交机器人' : 'Swarm 审查'),
  });

  if (isUpdate.value) {
    setFieldsValue({
      chatID: isLockGroup.value ? lockConfig.value?.lockChatID : messages.value?.[0]?.chatID,
    });
  }

  setModalProps({ confirmLoading: false });
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });
    const submitData = {
      streamID: streamID.value,
      chatID: isLockGroup.value ? undefined : values.chatID,
      lockChatID: isLockGroup.value ? values.chatID : undefined,
    };
    if (isLockGroup.value) {
      await setP4StreamLockConfigChat(userStore.getProjectId, submitData);
    } else if (!isUpdate.value) {
      await addSwarmReviewMessage(userStore.getProjectId, submitData);
    } else {
      await editSwarmReviewMessage(userStore.getProjectId, submitData, messages.value[0].ID!);
    }
    closeModal();
    emit('success');
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
