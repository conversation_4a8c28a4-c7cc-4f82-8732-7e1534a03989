<template>
  <ASpin :spinning="isLoading">
    <BasicTable class="apply-table" @register="registerTable" @change="change">
      <template #bodyCell="{ column, record }">
        <template v-if="column?.dataIndex === 'deviceName'">
          <EllipsisText class="w-300px cursor-pointer c-FO-Brand-Primary-Default" @click="clickDeviceName(record)">
            {{ record?.deviceName }}
          </EllipsisText>
        </template>
        <template v-if="column?.dataIndex === 'assetNo'">
          <EllipsisText class="w-full">
            {{ record?.assetNo }}
          </EllipsisText>
        </template>
        <template v-if="column?.dataIndex === 'deviceUID'">
          <EllipsisText class="w-full">
            {{ record?.deviceUID }}
          </EllipsisText>
        </template>
        <template v-if="column?.dataIndex === 'deviceType'">
          <EllipsisText class="w-full">
            {{ record?.deviceType }}
          </EllipsisText>
        </template>
        <template v-if="column?.dataIndex === 'chipset'">
          <EllipsisText
            v-tippy="'点击前往查看天梯图'" class="cursor-pointer c-FO-Brand-Primary-Default"
            @click="record.chipsetID && clickSocPK(record.chipsetID!)"
          >
            {{ record?.chipset?.socPK }}
          </EllipsisText>
        </template>
        <template v-if="column?.dataIndex === 'socName'">
          <EllipsisText class="w-200px">
            {{ record?.chipset?.brand?.nameCN }}/{{ record?.chipset?.socName }}
          </EllipsisText>
        </template>
        <template v-if="column?.dataIndex === 'usage'">
          {{ record?.usage?.label }}
        </template>
        <template v-if="column?.dataIndex === 'owner'">
          <ATooltip>
            <template #title>
              点击联系：{{ formatNickName(curDeviceAdmin(record)) }}
            </template>
            <span class="cursor-pointer c-FO-Brand-Primary-Default" @click="() => handleUserClick(curDeviceAdmin(record)?.ID)"> {{
              formatNickName(curDeviceAdmin(record)) + (isRecycled(record) ? ' (部门管理员)' : '') }}</span>
          </ATooltip>
        </template>
        <template v-if="column?.dataIndex === 'dept'">
          <EllipsisText class="w-full">
            {{ record.dept?.orgPath }}
          </EllipsisText>
        </template>
        <template v-if="column?.dataIndex === 'mobileType'">
          {{ formatMobileType(record?.mobileType) }}
        </template>
        <template v-if="column?.dataIndex === 'assetType'">
          {{ formatAssetType(record?.assetType) }}
        </template>
        <template v-if="column?.dataIndex === 'accessLevel'">
          <div
            class="flex flex-col items-start"
            :class="{ 'justify-center': ![DeviceAccessLevelEnum.DEPT, DeviceAccessLevelEnum.PROJECT].includes(record?.accessLevel || DeviceAccessLevelEnum.PUBLIC) }"
          >
            <div>
              <div
                v-if="canEditAccessLevel(record)" class="w-fit flex items-center gap-1"
                @click="handleEditAccessLevel(record, deptList)"
              >
                <span>
                  {{ formatAccessLevel(record?.accessLevel) }}
                </span>
                <ATooltip>
                  <template #title>
                    编辑
                  </template>
                  <a-button type="text" size="small" class="cursor-pointer !px-3px">
                    <Icon icon="icon-park-outline:edit" />
                  </a-button>
                </ATooltip>
              </div>
              <template v-else>
                {{ formatAccessLevel(record?.accessLevel) }}
              </template>
            </div>
            <div v-if="record?.accessLevel === DeviceAccessLevelEnum.DEPT" class="mt-2 flex flex-wrap gap-1">
              <ASelect
                :bordered="false" :dropdownStyle="{ display: 'none !important' }"
                :value="record?.accessDepts?.map((item: AccessDeptListItem) => item.deptID)" :showSearch="false" :options="record?.accessDepts"
                :maxTagCount="1" mode="multiple"
              >
                <template #tagRender="{ value: val }">
                  <ATag
                    v-tippy="formatDept(val, deptList, true) !== formatDept(val, deptList) ? formatDept(val, deptList, true) : undefined"
                    class="cursor-default b-1 b-[rgba(5,5,5,0.06)] bg-[rgba(0,0,0,0.06)] line-height-23px"
                  >
                    {{ formatDept(val, deptList) }}
                  </ATag>
                </template>
              </ASelect>
            </div>
            <div v-if="record?.accessLevel === DeviceAccessLevelEnum.PROJECT" class="mt-2 flex flex-wrap gap-1">
              <ASelect
                :bordered="false" :dropdownStyle="{ display: 'none !important' }"
                :value="record?.accessProjects?.map((item: AccessProjectListItem) => item.project?.name)" :showSearch="false"
                :options="record?.accessProjects" :maxTagCount="1" mode="multiple"
              >
                <template #tagRender="{ value: val }">
                  <ATag class="cursor-default b-1 b-[rgba(5,5,5,0.06)] bg-[rgba(0,0,0,0.06)] line-height-23px">
                    {{ val }}
                  </ATag>
                </template>
              </ASelect>
            </div>
          </div>
        </template>
        <template v-if="column?.dataIndex === 'currentUserID'">
          <ATooltip>
            <template #title>
              点击联系：{{ formatNickName(getUserById(record?.currentUserID)) }}
            </template>
            <span class="cursor-pointer c-FO-Brand-Primary-Default" @click="() => handleUserClick(record?.currentUserID)"> {{
              formatNickName(getUserById(record?.currentUserID)) }}</span>
          </ATooltip>
        </template>
        <template v-if="column?.dataIndex === 'remark'">
          <div class="flex">
            <ATooltip v-if="record?.remark">
              <template #title>
                <div style="white-space: pre-line">
                  {{ record?.remark || '无' }}
                </div>
              </template>
              <div class="line-clamp-3" style="white-space: pre-line">
                {{ record?.remark || '无' }}
              </div>
            </ATooltip>
            <div v-else>
              无
            </div>

            <ATooltip>
              <template #title>
                编辑
              </template>
              <a-button
                v-if="canEditAccessLevel(record)" title="编辑" type="text" size="small" class="!px-3px"
                @click="handleEditRemark(record)"
              >
                <Icon icon="icon-park-outline:edit" />
              </a-button>
            </ATooltip>
          </div>
        </template>
        <template v-if="column?.dataIndex === 'returnTime'">
          <div class="flex items-center justify-center gap-1">
            <span
              v-if="record.fsmState && ![DeviceFsmStateEnum.FREE].includes(record.fsmState) && record.latestApplyLog?.returnTime"
            >
              <ATag v-if="isOverdue(record.latestApplyLog?.returnTime)" color="red" :bordered="false" class="mr-2!">
                逾期
              </ATag>
              <span :class="isOverdue(record.latestApplyLog?.returnTime) ? 'c-FO-Functional-Error1-Default' : 'c-FO-Content-Text2'">
                {{ formatReturnTime(record.latestApplyLog?.returnTime || 0, true) }}
              </span>
            </span>
            <Icon
              v-if="!isSettingsPage && record.fsmState !== DeviceFsmStateEnum.FREE && (hasAllPermission(record) || userStore.isITAssetManagement)"
              v-tippy="'更改预计归还时间'" :icon="EditIcon"
              class="cursor-pointer c-FO-Content-Text2 hover:c-FO-Content-Text1"
              @click.stop="() => handleApply(record, ApplyTypeEnum.CHANGE_RETURN_TIME)"
            />
            <Icon
              v-if="record.fsmState !== DeviceFsmStateEnum.FREE && record.currentUserID !== userStore.getUserInfo.ID && !isCurDeviceAdmin(record)"
              v-tippy="record.isSubscribed ? '取消订阅“空闲提醒”' : '订阅“空闲提醒”'" icon="material-symbols:bookmark-star-rounded"
              :class="record.isSubscribed ? 'c-FO-Brand-Primary-Default hover:c-FO-Brand-Primary-Hover' : 'c-FO-Content-Text2 hover:c-FO-Content-Text3'"
              class="cursor-pointer"
              @click.stop="() => handleSubscribe(record)"
            />
          </div>
        </template>
        <template v-if="column?.dataIndex === 'useDays'">
          <div v-if="record.fsmState === DeviceFsmStateEnum.USING || record.fsmState === DeviceFsmStateEnum.RETURNING">
            {{ compareDate(new Date().toISOString().slice(0, 10), record?.latestUsageLog?.CreatedAt.slice(0, 10), 'day')
            }}天
          </div>
          <div v-else>
            -
          </div>
        </template>
        <template v-if="column?.dataIndex === 'fsmState'">
          <div v-if="!isSettingsPage" class="flex">
            <Icon icon="icon-park-outline:dot" :class="getState(record.fsmState)[0]" />
            <span> {{ getState(record.fsmState)[1] }}</span>
          </div>
          <div v-else class="flex">
            <Icon v-if="record.online" icon="icon-park-outline:dot" :class="getState(record.fsmState)[0]" />
            <Icon v-else icon="carbon:dot-mark" class="!text-gray-400" />
            <span v-if="record.online">{{ getState(record.fsmState)[1] }}</span>
            <span v-else class="text-gray-400">离线</span>
          </div>
        </template>
        <template v-if="column?.dataIndex === 'action'">
          <div class="flex justify-center">
            <BasicButton
              v-if="isSettingsPage" type="link" class="!c-FO-Brand-Primary-Default"
              @click.stop="() => clickDeviceName(record)"
            >
              编辑
            </BasicButton>
            <div v-if="isSettingsPage" class="flex items-center gap-2">
              <APopconfirm title="确定要删除该设备吗？" @confirm="() => handleDelete(record)">
                <BasicButton type="link" :disabled="record.online" size="small" @click.stop>
                  <span v-if="!record.online" class="!c-FO-Functional-Error1-Default">删除</span>
                  <ATooltip v-else>
                    <template #title>
                      仅支持删除离线设备
                    </template>
                    删除
                  </ATooltip>
                </BasicButton>
              </APopconfirm>
            </div>
            <div v-else>
              <template v-if="canBorrow(record)">
                <BasicButton
                  v-if="isCurDeviceAdmin(record)" type="link" class="w-90px !c-FO-Brand-Primary-Default"
                  @click.stop="() => handleApply(record, ApplyTypeEnum.DIRECT_BORROW)"
                >
                  借出
                </BasicButton>
                <BasicButton
                  v-else-if="!isUnavailable(record)" type="link" class="w-90px !c-FO-Brand-Primary-Default"
                  @click.stop="() => handleApply(record, ApplyTypeEnum.BORROW_APPLY)"
                >
                  借用
                </BasicButton>
              </template>
              <template v-if="canReturn(record)">
                <BasicButton
                  v-if="isCurDeviceAdmin(record)" type="link" class="w-90px !c-FO-Functional-Success1-Default"
                  @click.stop="() => handleDirectReturn(record)"
                >
                  确认归还
                </BasicButton>
                <BasicButton
                  v-else class="w-90px !c-FO-Functional-Success1-Default" type="link"
                  @click.stop="() => handleApply(record, ApplyTypeEnum.RETURN_APPLY)"
                >
                  归还申请
                </BasicButton>
              </template>
              <APopconfirm
                v-if="canPickUp(record)" title="确认你已借出该设备吗？" placement="topRight" okText="确认"
                @confirm="() => handlePickUp(record)"
              >
                <BasicButton class="w-90px !c-FO-Functional-Success1-Default" type="link" @click.stop>
                  确认借出
                </BasicButton>
              </APopconfirm>
              <BasicButton
                v-if="canAuditReturn(record) || canAudit(record)" class="w-90px !c-FO-Functional-Error1-Default" type="link"
                @click.stop="() => handleApply(record, canAuditReturn(record) ? ApplyTypeEnum.RETURN_AUDIT : ApplyTypeEnum.BORROW_AUDIT)"
              >
                审批
              </BasicButton>
            </div>
            <BasicButton
              v-if="!isSettingsPage" type="link" class="w-90px px-0 c-gray6 hover:c-#000!"
              @click.stop="() => clickDeviceName(record)"
            >
              详情
            </BasicButton>
          </div>
        </template>
      </template>
    </BasicTable>
    <AccessLevelModal @register="registerAccessLevelModal" @success="handleAccessLevelSuccess" />
    <RemarkModalHolder />
  </ASpin>
</template>

<script lang="ts" setup>
import { type BasicColumn, BasicTable, useTable } from '/@/components/Table';
import { Icon } from '/@/components/Icon';
import { Popconfirm as APopconfirm, Select as ASelect, Spin as ASpin, Tag as ATag, Tooltip as ATooltip, message } from 'ant-design-vue';
import { DeviceAccessLevelEnum, DeviceFsmStateEnum } from '/@/api/page/model/deptAssetModel';
import type { AccessDeptListItem, AccessProjectListItem, DeviceBrandListItem, DeviceListItem, DevicesViewModel } from '/@/api/page/model/deptAssetModel';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { deleteDevice, editPartialDevice, getAssetDevicesViewApi, getDeviceBrandsListByPage } from '/@/api/page/deptAsset';
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { accessLevelList, assetTypeOptions, mobileTypeOptions } from './device.data';
import { useDeptAssetApply } from './hook';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useUserStore } from '/@/store/modules/user';
import { compareDate } from '/@/utils/dateUtil';
import { ApplyTypeEnum } from './apply.data';
import { BasicButton } from '/@/components/Button';
import { usetableView } from './tableView.hook';
import { useModal } from '/@/components/Modal';
import AccessLevelModal from './detail/AccessLevelModal.vue';
import { useModalShow } from '@hg-tech/utils-vue';
import RemarkModal from './detail/RemarkModal.vue';
import EditIcon from '@iconify-icons/icon-park-outline/edit';
import type { DeptListItem } from '/@/api/page/model/systemModel';
import EllipsisText from '../../../components/EllipsisText/src/EllipsisText.vue';

const props = withDefaults(defineProps<{
  // 表格数据
  deviceList?: DeviceListItem[];
  deptList?: any[];
  nowTab?: 'allDevice' | 'myDevice' | 'deviceManage' | 'myOccupy';
  total?: number;
  tablePage?: { defaultCurrent?: number; defaultPageSize?: number; total?: number; todoTotal?: number };
  viewSetting: DevicesViewModel;
  isSettingsPage?: boolean;
  onlyTodoDevice?: boolean;
}>(), {
  isSettingsPage: true,
  deviceList: () => [],
  deptList: () => [],
  nowTab: 'allDevice',
  total: 0,
  tablePage: () => ({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
    todoTotal: 0,
  }),
  viewSetting: () => ({}),
  onlyTodoDevice: false,
});

const emits = defineEmits<{
  clickDeviceName: [DeviceListItem];
  clickSocPK: [number];
  accessLevelSuccess: [];
  handleApplyEmit: [DeviceListItem, ApplyTypeEnum];
  handleDirectReturnEmit: [DeviceListItem];
  handlePickUpEmit: [DeviceListItem];
  handleSubscribeEmit: [DeviceListItem];
  pageChangeEmit: [any];
  deleteSuccess: [];
  changeCheckedKeys: [number[]];
  filedSuccess: [];
}>();
const checkedKeys = ref<number[]>([]);
const selectedRowKeysItem = ref<(string | number)[]>([]);
const isLoading = ref(false);
const [registerAccessLevelModal, { openModal: openAccessLevelModal }] = useModal();
const deviceListComputed = ref();
const { getUserById, hasAllPermission, isRecycled, curDeviceAdmin, formatDept, isOverdue, isCurDeviceAdmin, isUnavailable, canBorrow, canPickUp, canReturn, canAudit, canAuditReturn, formatReturnTime, handleUserClick } = useDeptAssetApply();
const { getColumns } = usetableView();
const [RemarkModalHolder, showRemarkModal] = useModalShow(RemarkModal);
const [registerTable, { setColumns, setPagination, scrollTo, redoHeight }] = useTable(props.isSettingsPage || props.nowTab === 'myDevice'
  ? {
    columns: [],
    striped: false,
    dataSource: deviceListComputed,
    showIndexColumn: false,
    clickToRowSelect: false,
    resizeHeightOffset: 80,
    scroll: {
      x: '70%',
    },
    rowSelection: computed(() => ({
      type: 'checkbox',
      onChange: onSelectChange,
      selectedRowKeys: selectedRowKeysItem.value,
    })),
    tableLayout: 'fixed',
  }
  : {
    columns: [],
    dataSource: deviceListComputed,
    striped: false,
    showIndexColumn: false,
    clickToRowSelect: false,
    resizeHeightOffset: 40,
    tableLayout: 'fixed',
    scroll: {
      x: '70%',
    },
  });

function getState(fsmState: number) {
  if (fsmState === DeviceFsmStateEnum.FREE) {
    return ['c-#62d256', '空闲'];
  } else if (fsmState === DeviceFsmStateEnum.APPLYING) {
    return ['c-#245bdb', '申请中'];
  } else if (fsmState === DeviceFsmStateEnum.BORROWING) {
    return ['c-#de7802', '领用中'];
  } else if (fsmState === DeviceFsmStateEnum.USING) {
    return ['c-#d83931', '使用中'];
  } else if (fsmState === DeviceFsmStateEnum.RETURNING) {
    return ['c-#6425d0', '归还中'];
  } else {
    return ['c-#646a73', '离线'];
  }
}
const userStore = useUserStore();
const brandList = ref<DeviceBrandListItem[]>([]);

/** 获取设备品牌列表 */
async function getDeviceBrandList() {
  const { list } = await getAllPaginationList(getDeviceBrandsListByPage);
  brandList.value = list || [];
}

/** 是否可以编辑流通级别 */
function canEditAccessLevel(device?: DeviceListItem) {
  if (!device) {
    return false;
  }
  return hasAllPermission(device) || userStore.isITAssetManagement;
}

function formatMobileType(val?: number) {
  return mobileTypeOptions.find((e) => e.value === val)?.label || '';
}

function formatAssetType(val?: number) {
  return assetTypeOptions.find((e) => e.value === val)?.label || '';
}

function formatBrand(val?: number) {
  const brand = brandList.value.find((e) => e.ID === val);
  return brand?.cnName ? `${brand.cnName} (${brand.name})` : brand?.name;
}

function changeFilesColumns(columns: BasicColumn[]) {
  // 字段被修改
  nextTick(async () => {
    columns.forEach((item) => {
      item.fixed = getColumns(props.nowTab).find((defaultItem) => defaultItem.dataIndex === item.dataIndex)?.fixed || false;
      item.width = getColumns(props.nowTab).find((defaultItem) => defaultItem.dataIndex === item.dataIndex)?.width || 0;
      item.title = getColumns(props.nowTab).find((defaultItem) => defaultItem.dataIndex === item.dataIndex)?.title || '';
    });
    await setColumns(columns);

    // 将横向滚动条滚动到开始位置
    const tableBodyEl = document.querySelector('.apply-table .ant-table-body');
    if (tableBodyEl) {
      tableBodyEl.scrollLeft = 0;
    }
    // 同时处理表头的滚动位置
    const tableHeaderEl = document.querySelector('.apply-table .ant-table-header');
    if (tableHeaderEl) {
      tableHeaderEl.scrollLeft = 0;
    }
  });
}

function onSelectChange(selectedRowKeys: (string | number)[], row: DeviceListItem[]) {
  selectedRowKeysItem.value = selectedRowKeys;
  checkedKeys.value = row.map((item) => item?.ID).filter((id): id is number => id !== undefined);
  emits('changeCheckedKeys', checkedKeys.value);
}

// 流通级别名称获取
function formatAccessLevel(val?: number) {
  return accessLevelList.find((e) => e.value === val)?.label || '';
}

function clickDeviceName(record: DeviceListItem) {
  emits('clickDeviceName', record);
}

function clickSocPK(id: number) {
  emits('clickSocPK', id);
}

function handleEditAccessLevel(record: DeviceListItem, deptList: DeptListItem[]) {
  openAccessLevelModal(true, {
    record,
    deptList,
    isRowSelection: false,
  });
}

function handleAccessLevelSuccess() {
  checkedKeys.value = [];
  selectedRowKeysItem.value = [];
  emits('accessLevelSuccess');
}

async function handleDelete(record: DeviceListItem) {
  try {
    if (!record?.ID) {
      throw new Error('无效设备ID');
    }
    isLoading.value = true;
    await deleteDevice(record.ID);
    message.success('删除成功');
    emits('deleteSuccess');
  } catch (e: any) {
    message.error(`删除失败: ${e.message}`);
  } finally {
    isLoading.value = false;
  }
}

function handleFiledSuccess() {
  emits('filedSuccess');
  checkedKeys.value = [];
}

/** 备注修改 */
async function handleEditRemark(record: DeviceListItem) {
  await showRemarkModal({
    remark: record.remark || '',
    async sentReq(remark: string) {
      return editPartialDevice(record.ID!, {
        remark,
      }).then((res) => res?.data?.data);
    },
  });
  handleFiledSuccess();
}

function handleApply(record: DeviceListItem, type: ApplyTypeEnum) {
  emits('handleApplyEmit', record, type);
}

function handleDirectReturn(record: DeviceListItem) {
  emits('handleDirectReturnEmit', record);
}

function handlePickUp(record: DeviceListItem) {
  emits('handlePickUpEmit', record);
}

function handleSubscribe(record: DeviceListItem) {
  emits('handleSubscribeEmit', record);
}

function change(page: any) {
  emits('pageChangeEmit', page);
  // 回显checkedKeys
}

onMounted(async () => {
  try {
    isLoading.value = true;
    await getDeviceBrandList();
    await setPagination(props.tablePage);
    // 通过接口获取view
    const { view } = await getAssetDevicesViewApi();

    const viewSetting: DevicesViewModel = JSON.parse(view || '{}');
    viewSetting?.[props.nowTab]?.forEach((item) => {
      item.fixed = getColumns(props.nowTab).find((defaultItem) => defaultItem.dataIndex === item.dataIndex)?.fixed || false;
      item.width = getColumns(props.nowTab).find((defaultItem) => defaultItem.dataIndex === item.dataIndex)?.width || 0;
      item.title = getColumns(props.nowTab).find((defaultItem) => defaultItem.dataIndex === item.dataIndex)?.title || '';
    });
    getColumns(props.nowTab).forEach((item) => {
      if (!viewSetting?.[props.nowTab]?.find((viewSettingItem) => viewSettingItem.dataIndex === item.dataIndex)) {
        viewSetting[props.nowTab]?.splice((viewSetting[props.nowTab]?.length || 0) - 2, 0, item);
      }
    });
    if (viewSetting?.[props.nowTab]) {
      setColumns(viewSetting[props.nowTab] || []);
    } else {
      setColumns(getColumns(props.nowTab) || []);
    }
    deviceListComputed.value = props.deviceList.map((i) => ({ ...i, key: i.ID }));
  } finally {
    isLoading.value = false;
  }
});

function redoHeightFn() {
  redoHeight();
}

watch(() => props.tablePage, async () => {
  await setPagination(props.tablePage);
  // 表格滚动条滚动到顶部
  if (deviceListComputed.value) {
    scrollTo('top');
  }
}, {
  deep: true,
});

watch(() => props.deviceList, () => {
  deviceListComputed.value = props.deviceList.map((i) => ({ ...i, key: i.ID }));
});

defineExpose({ changeFilesColumns, checkedKeys, selectedRowKeysItem, redoHeightFn });
</script>

<style lang="less" scoped>
.apply-table {
  :deep(.ant-select-selection-overflow-item) {
    margin-right: 4px;
    margin-top: 4px;
  }

  :deep(.ant-table-cell.ant-table-cell-row-hover) {
    background-color: @FO-Container-Fill2 !important;
  }
  :deep(.ant-table-row-selected .ant-table-cell) {
    background: @FO-Brand-Tertiary-Active !important;
  }
}
</style>
