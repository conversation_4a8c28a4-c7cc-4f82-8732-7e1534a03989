<template>
  <BasicModal
    :wrapClassName="prefixCls"
    width="700px"
    okText="保存"
    destroyOnClose
    :maskClosable="false"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <template #title>
      <div class="w-full flex justify-center">
        Review和关注组参与干员配置
      </div>
    </template>
    <BasicForm @register="registerForm">
      <template #subGroups="{ model, field }">
        <ATreeSelect
          v-model:value="model[field]"

          showCheckedStrategy="SHOW_PARENT"
          :treeData="groupList"

          showSearch allowClear treeCheckable showArrow
          mode="multiple"
          placeholder="请选择干员组"
        >
          <template #tagRender="{ label, closable, onClose }">
            <GroupUsersPopover :groupName="label" :serverID="serverID">
              <ATag :closable="closable" class="!text-sm" @close="onClose">
                {{ label }}
              </ATag>
            </GroupUsersPopover>
          </template>
        </ATreeSelect>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts"  setup>
import { Tag as ATag, TreeSelect as ATreeSelect } from 'ant-design-vue';
import { pick } from 'lodash-es';
import { ref } from 'vue';
import type { SelectOption } from '../swarmSettings.data.tsx';
import { auditFormSchema } from '../swarmSettings.data.tsx';
import type {
  SwarmWorkflowGlobalsItem,
} from '../../../../api/page/model/swarmModel';
import { getPerforceServerGroups } from '../../../../api/page/perforce';
import {
  addSwarmReviewProject,
  addSwarmWorkflowGlobal,
  editSwarmReviewProject,
  editSwarmWorkflowGlobal,
  getSwarmWorkflowGlobals,
} from '../../../../api/page/swarm';
import { BasicForm, useForm } from '../../../../components/Form';
import GroupUsersPopover from '../../../../components/GroupUsersPopover';
import { type ModalMethods, BasicModal, useModalInner } from '../../../../components/Modal';
import { useDesign } from '../../../../hooks/web/useDesign';
import { useUserStoreWithOut } from '../../../../store/modules/user';

const emit = defineEmits<{
  (event: 'success'): void;
  (event: 'register', modalMethod: ModalMethods, uuid: number): void;
}>();

const { prefixCls } = useDesign('swarm-audit-project-modal');

const isUpdate = ref(false);
const editId = ref();
const userStore = useUserStoreWithOut();
const serverID = ref<number>();
const streamID = ref();
const auditInfo = ref();
const systemP4GroupList = ref<SelectOption[]>([]);
const groupList = ref<{ label: string; value: string }[]>([]);

const swarmWorkflowGlobal = ref<SwarmWorkflowGlobalsItem>();

async function getSwarmWorkflowGlobalConfig() {
  const { globalWorkflow } = await getSwarmWorkflowGlobals(userStore.getProjectId, serverID.value);
  swarmWorkflowGlobal.value = globalWorkflow || {};
}

async function getCurServerP4Groups() {
  const { groups } = await getPerforceServerGroups(serverID.value!);
  if (groups?.length > 0) {
    systemP4GroupList.value = groups.map((e) => ({
      label: e,
      value: e,
    }));
  } else {
    systemP4GroupList.value = [];
  }
}

const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
  labelWidth: 100,
  schemas: auditFormSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 22, offset: 1 },
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });
  await resetFields();
  isUpdate.value = data?.isUpdate;
  streamID.value = data?.stream?.ID;
  editId.value = data?.auditInfo?.ID;
  auditInfo.value = data?.auditInfo;
  groupList.value = data?.groupList;
  serverID.value = data?.serverID;
  await getCurServerP4Groups();
  await getSwarmWorkflowGlobalConfig();
  if (isUpdate.value) {
    await setFieldsValue({
      ...data.auditInfo,
      members: data.auditInfo?.members || [],
      subGroups: data.auditInfo?.subGroups || [],
    });
  }
  if (swarmWorkflowGlobal.value?.ID) {
    await setFieldsValue({
      ignoreGroups: swarmWorkflowGlobal.value?.ignoreGroups || [],
      ignoreUsers: swarmWorkflowGlobal.value?.ignoreUsers || [],
    });
  }

  setModalProps({ confirmLoading: false });
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });
    const submitData = Object.assign(
      { streamID: streamID.value },
      pick(values, ['members', 'subGroups']),
    );
    if (!isUpdate.value) {
      await addSwarmReviewProject(userStore.getProjectId, submitData);
    } else if (editId.value) {
      const { swarmProjectID, swarmProjectName } = auditInfo.value;
      await editSwarmReviewProject(
        userStore.getProjectId,
        Object.assign({ swarmProjectID, swarmProjectName }, submitData),
        editId.value,
      );
    }
    if (swarmWorkflowGlobal.value?.ID) {
      await editSwarmWorkflowGlobal(
        userStore.getProjectId,
        {
          ignoreGroups: values.ignoreGroups,
          ignoreUsers: values.ignoreUsers,
          serverID: serverID.value,
        },
        swarmWorkflowGlobal.value.ID,
      );
    } else {
      await addSwarmWorkflowGlobal(userStore.getProjectId, {
        ignoreGroups: values.ignoreGroups,
        ignoreUsers: values.ignoreUsers,
        serverID: serverID.value,
      });
    }
    closeModal();
    emit('success');
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-swarm-audit-project-modal';
// .@{prefix-cls} {
// }
</style>
