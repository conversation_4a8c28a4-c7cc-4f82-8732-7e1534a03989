<template>
  <PageWrapper headerSticky footerSticky @back="goBack">
    <template #title>
      <div class="flex items-center gap-4">
        <slot name="title" :streamName="streamName">
          <span>提交检查：{{ streamName }}</span>
        </slot>
        <slot v-if="curStream?.commitCheckEnabled" name="titleAfter" />
      </div>
    </template>
    <slot v-if="curStream?.commitCheckEnabled" />
    <div v-else-if="isCloning" class="m-4 rounded-md bg-FO-Container-Fill1 p-4">
      <div class="my-10 flex items-center justify-center">
        <Icon icon="devGuard-submit-check|svg" :size="80" />
        <div class="ml-8">
          <div class="c-FO-Content-Text1">
            从已存在分支中克隆提交检查配置:
          </div>
          <div class="my-5 w-300px flex items-center">
            <div class="w-80px">
              <div class="text-sm font-bold">
                分支：
              </div>
              <div class="text-xs c-FO-Content-Text2">
                克隆分支
              </div>
            </div>
            <a-select
              v-model:value="copyFromStreamID"
              class="w-full"
              showSearch
              optionFilterProp="description"
              :options="copyStreamList"
              placeholder="请选择源分支"
              :fieldNames="{
                label: 'description',
                value: 'ID',
              }"
              allowClear
            />
          </div>
          <div class="mt-4">
            <a-button type="primary" :loading="isLoading" @click="copyCheck">
              克隆
            </a-button>
            <a-button class="ml-4" @click="handleCancel">
              取消
            </a-button>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="m-4 rounded-md bg-FO-Container-Fill1 p-4">
      <div class="my-10 w-full flex flex-col items-center justify-center">
        <div class="text-lg c-FO-Content-Text2">
          该分支未配置提交检查
        </div>
        <div class="mt-6">
          <a-button type="primary" :loading="isLoading" @click="handleConfig">
            配置
          </a-button>
          <tippy
            :content="!copyStreamList?.length ? '暂无可克隆的分支' : undefined"
            :offset="[0, 16]"
          >
            <a-button
              class="ml-3"
              type="primary"
              :disabled="!copyStreamList?.length"
              @click="isCloning = true"
            >
              克隆其他分支的配置
            </a-button>
          </tippy>
        </div>
      </div>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup>
import { useTimeoutFn } from '@vueuse/shared';
import { computed, onBeforeMount, ref, unref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useP4DepotStream } from '../versionControl/p4PermissionManage/hook';
import type { StreamsListItem } from '/@/api/page/model/p4Model';
import { addSubmitConfig, cloneSubmitConfig } from '/@/api/page/p4';
import Icon from '/@/components/Icon';
import { PageWrapper } from '/@/components/Page';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGo } from '/@/hooks/web/usePage';
import { useTabs } from '/@/hooks/web/useTabs';
import { useP4StoreWithOut } from '/@/store/modules/p4';
import { useUserStoreWithOut } from '/@/store/modules/user';

defineOptions({
  name: 'ResourceCheckTab',
});

const go = useGo();
const { currentRoute } = useRouter();
const userStore = useUserStoreWithOut();
const p4Store = useP4StoreWithOut();
const { allStreamList, getAllStreamList } = useP4DepotStream();
const depotID = Number(unref(currentRoute).params.id);
const curStream = ref<StreamsListItem>();
const streamID = Number(unref(currentRoute).params.stream_id);
const streamName = ref<string>('');
const isLoading = ref(false);
const isCloning = ref(false);
const copyFromStreamID = ref<number>();
const { refreshPage } = useTabs();

// 复制分支权限的分支下拉框, 过滤掉没有配置完成的分支和虚拟分支
const copyStreamList = computed(() => allStreamList.value?.filter((e) => e.commitCheckEnabled && e.streamType !== 4));

// 获取分支列表
async function getStreamList() {
  await getAllStreamList(depotID);
  curStream.value = allStreamList.value?.find((e) => e.ID === streamID);

  if (curStream.value) {
    p4Store.setCurStream(curStream.value);
    streamName.value = curStream.value?.description || curStream.value?.path || '';
  }
}

onBeforeMount(() => {
  getStreamList();
});

async function handleConfig() {
  if (!userStore.getProjectId) {
    return;
  }

  isLoading.value = true;
  await addSubmitConfig(userStore.getProjectId, streamID);
  useTimeoutFn(() => {
    refreshPage();
    isLoading.value = false;
  }, 500);
}

function handleCancel() {
  isCloning.value = false;
  copyFromStreamID.value = undefined;
}

async function copyCheck() {
  if (!userStore.getProjectId) {
    return;
  }

  if (!copyFromStreamID.value) {
    const { createMessage } = useMessage();

    createMessage.warning('请选择源分支');

    return;
  }

  const res = await cloneSubmitConfig(userStore.getProjectId, {
    srcStreamID: copyFromStreamID.value,
    dstStreamID: streamID,
  });

  if (res?.code !== 7) {
    useTimeoutFn(() => {
      refreshPage();
      isCloning.value = false;
    }, 500);
  } else {
    isCloning.value = false;
  }
}

// 页面左侧点击返回链接时的操作
function goBack() {
  go(
    currentRoute.value.name === 'ResourceCheck'
      ? { name: 'P4Depots' }
      : { name: 'ResourceCheck', query: { t: 2 } },
  );
}

defineExpose({
  getStreamList,
});

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      go({ name: 'P4Depots' });
    }
  },
);
</script>
