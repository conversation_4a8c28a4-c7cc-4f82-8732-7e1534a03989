/**
 * Merge权限点
 * @see test： https://forgeon-test.hypergryph.net/permission-center/108?fromSearchKw=&p=26&tenantId=239
 */
enum MergePermission {
  // page
  ViewMergeHistory = 'ViewMergeHistory',
  ViewTask = 'ViewTask',
  // operation
  EditMerge = 'EditMerge',
  ViewRule = 'ViewRule',
  AddRule = 'AddRule',
  DeleteRule = 'DeleteRule',
  EditRule = 'EditRule',
  PauseAndResume = 'PauseAndResume',
  ResolveOnline = 'ResolveOnline',
  ResolveLocal = 'ResolveLocal',
  Assign = 'Assign',
  Retry = 'Retry',
  Skip = 'Skip',
  ShowSwarm = 'ShowSwarm',
  ViewUserAction = 'ViewUserAction',
  /**
   * 一键拉群
   */
  CreateGroupChat = 'CreateGroupChat',
}

export {
  MergePermission,
};
