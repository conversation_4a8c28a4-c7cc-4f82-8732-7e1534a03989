<template>
  <BasicModal
    :wrapClassName="prefixCls"
    width="750px"
    :footer="null"
    :class="`${prefixCls}`"
    :maskClosable="false"
    :afterClose="handleClose"
    @register="registerModal"
  >
    <template #title>
      <div class="w-full flex items-center justify-center text-xl">
        review配置
      </div>
    </template>
    <BorderBox class="mx-[24px]" subLabel="未配置reviewer时，即使开启了review配置，也不会创建Swarm">
      <template #title="{ titleClass }">
        <span :class="titleClass" class="FO-Font-B18">reviewer</span>
      </template>
      <BasicForm @register="registerFormAudit">
        <template #selectGroup="{ model, field }">
          <TreeSelect
            v-model:value="model[field]"
            treeCheckable
            showCheckedStrategy="SHOW_PARENT"
            :treeData="groupList"
            showSearch
            allowClear
            showArrow
            mode="multiple"
            placeholder="请选择干员组"
          >
            <template #tagRender="{ label, closable, onClose }">
              <GroupUsersPopover :groupName="label" :serverID="serverID">
                <Tag :closable="closable" class="FO-Font-b14" @close="onClose">
                  {{ label }}
                </Tag>
              </GroupUsersPopover>
            </template>
          </TreeSelect>
        </template>
      </BasicForm>
    </BorderBox>

    <BorderBox class="mx-[24px]">
      <template #title="{ titleClass }">
        <span :class="titleClass" class="FO-Font-B18">review超时提示</span>
      </template>
      <template #subTitle="{ subTitleClass }">
        <div :class="subTitleClass">
          <div>review通知发出后，若超过时限没有处理，则发送review超时提示;</div>
          <div>清空时限或未配置提示通知人/群则不会发送review超时提示。</div>
        </div>
      </template>
      <TimeInput v-model:time="timeoutFormValue.timeout" />
      <div class="mb-[8px]">
        <div class="mb-[4px]">
          单独通知人
        </div>
        <Select
          v-model:value="timeoutFormValue.notifier"
          placeholder="对应reviewer"
          mode="multiple"
          class="w-full"
          :options="notifyGroupOptions"
          :showSearch="true"
          :filterOption="userFilterOption"
        />
      </div>
      <div class="mb-[8px]">
        <div class="mb-[4px]">
          通知群
        </div>
        <Select
          v-model:value="timeoutFormValue.notifyGroups"
          mode="multiple"
          class="w-full"
          :options="chatList"
          placeholder="请选择"
          :fieldNames="{ label: 'name', value: 'chat_id' }"
        />
        <span class="FO-Font-b12 c-#8d8d8d">*需先将 <b>提交机器人</b> 应用添加至群中，之后刷新页面会显示选项</span>
      </div>
    </BorderBox>
    <div class="mb-[2px] mt-[16px] w-full flex items-center justify-center">
      <Button
        type="primary"
        class="mr-6 !rounded"
        @click="handleSubmit"
      >
        保存
      </Button>
      <Button class="!rounded" @click="closeModal">
        取消
      </Button>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import type { TreeSelectProps } from 'ant-design-vue';
import type { SwarmGroupItemInfo } from '../../../../api/page/model/swarmModel.ts';
import type { FeishuChatListItem } from '../../../../api/page/model/systemModel.ts';
import { Button, Select, Tag, TreeSelect } from 'ant-design-vue';
import { computed, ref, shallowRef, watch } from 'vue';
import { type ModalMethods, BasicModal, useModalInner } from '../../../../components/Modal';
import { BasicForm, BorderBox, useForm } from '../../../../components/Form';
import { useDesign } from '../../../../hooks/web/useDesign';
import { useMessage } from '../../../../hooks/web/useMessage';
import { useUserStoreWithOut } from '../../../../store/modules/user';
import GroupUsersPopover from '../../../../components/GroupUsersPopover';
import { useRouter } from 'vue-router';
import { editSwarmGroup } from '../../../../api/page/swarm.ts';
import { useTrack } from '../../../../hooks/system/useTrack.ts';
import TimeInput from '../TimeInput/TimeInput.vue';
import { useTimeoutConfig } from '../useTimeoutConfig.ts';
import { userFilterOption } from '../../../../hooks/system/useUserList.ts';

const emit = defineEmits<{
  (e: 'success', groupId?: number): void;
  (e: 'register', modalInstance: ModalMethods, uuid: number): void;
}>();

const { prefixCls } = useDesign('swarm-group-audit-modal');
const { createMessage } = useMessage();
const { currentRoute } = useRouter();
const { setTrack } = useTrack();
const serverID = computed(() => Number(currentRoute.value.query.sID));

const { notifyGroupOptions, getUserList, splitOptionValues } = useTimeoutConfig('review');

const timeoutFormValue = ref<{
  timeout: number | undefined;
  notifier: (string | number)[];
  notifyGroups: string[];
}>({
      timeout: undefined,
      notifier: [],
      notifyGroups: [],
    });

const reviewProjectID = ref<number>();
const swarmGroup = shallowRef<SwarmGroupItemInfo>();
const reviewGroup = computed(() => swarmGroup.value?.reviewGroup);
const streamID = ref<number>();
const groupList = shallowRef<TreeSelectProps['treeData']>([]);
const chatList = shallowRef<FeishuChatListItem[]>([]);
const userStore = useUserStoreWithOut();

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });

  swarmGroup.value = data.swarmGroup;
  chatList.value = data?.chatList;
  groupList.value = data?.groupList;
  streamID.value = data?.streamID;
  reviewProjectID.value = data?.reviewProjectID;

  await getUserList();
  setModalProps({ confirmLoading: false });
});

const [registerFormAudit, { setFieldsValue: setAuditFormValue, validate: validateAuditForm }] = useForm({
  labelWidth: 130,
  layout: 'vertical',
  schemas: computed(() => [
    {
      field: 'defaultReviewerIDs',
      label: '添加干员',
      component: 'UserSelect',
      componentProps: {
        isMultiple: true,
      },
    },
    {
      field: 'defaultReviewerGroups',
      component: 'Select',
      label: '添加干员组',
      slot: 'selectGroup',
    },
    {
      field: 'isReviewers',
      label: '',
      component: 'Checkbox',
      renderComponentContent: '将所有reviewer作为Swarm跟进人',
    },
    {
      field: 'isModerators',
      label: '',
      component: 'Checkbox',
      renderComponentContent: '仅reviewer可通过Swarm',
    },
  ]),
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
});

watch(reviewGroup, (v) => {
  setAuditFormValue({
    isReviewers: v?.isReviewers ?? false,
    isModerators: v?.isModerators ?? false,
    defaultReviewerIDs: (v?.defaultReviewers)?.map((i) => i.sysUserId),
    defaultReviewerGroups: v?.defaultReviewerGroups ?? [],
  });
  timeoutFormValue.value = {
    timeout: v?.timeoutConfig?.timeLimit,
    notifier: [
      ...(v?.timeoutConfig?.additionalNotifier?.specialGroups || []),
      ...(v?.timeoutConfig?.additionalNotifier?.members || []),
    ],
    notifyGroups: v?.timeoutConfig?.additionalNotifyChatGroupIDs || [],
  };
});

async function handleSubmit() {
  if (!reviewGroup.value?.ID) {
    return;
  }
  const auditData = await validateAuditForm();
  const res = await editSwarmGroup(userStore.getProjectId, {
    swarmReviewGroupID: swarmGroup.value?.swarmReviewGroup?.ID,
    streamID: streamID.value,
    reviewerIDs: auditData.defaultReviewerIDs,
    reviewerGroups: auditData.defaultReviewerGroups,
    reviewerBindSwarmOption: auditData.isReviewers,
    onlyReviewerApproveOption: auditData.isModerators,
    timeoutConfig: {
      timeLimit: timeoutFormValue.value.timeout,
      additionalNotifier: splitOptionValues(timeoutFormValue.value.notifier, 'additionalNotifierSpecialGroups'),
      additionalNotifyChatGroupIDs: timeoutFormValue.value.notifyGroups,
    },
  });

  if (res?.code === 7) {
    return;
  }

  setTrack('ydnsgqmpjo');
  createMessage.success('更新成功');

  closeModal();
}

async function handleClose() {
  emit('success', reviewGroup.value?.ID);
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-swarm-group-audit-modal';
.@{prefix-cls} {
  &__radio {
    display: flex;
    margin-top: 8px;
    font-size: 18px;
    font-weight: bold !important;
  }

  .ant-form-item {
    margin-bottom: 0 !important;
  }
}
</style>
