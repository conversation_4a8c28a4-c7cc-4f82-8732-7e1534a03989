<template>
  <div :class="prefixCls">
    <template v-if="!needWorkspace">
      <BasicForm class="!my-6" @register="registerForm" />
      <div class="w-full flex justify-center">
        <a-button
          :type="!isExistStream ? 'success' : 'warning'"
          :loading="loading"
          @click="handleCreateBranch"
        >
          {{ isExistStream ? '添加分支' : '新建分支' }}
        </a-button>
      </div>
    </template>
    <template v-else>
      <div class="my-6 w-full flex items-center justify-center">
        <div> <SvgIcon name="devGuard-stream-created-success" :size="80" /></div>
        <div class="ml-6">
          <div class="mt-6 text-xl">
            分支已创建, Task分支需要指定工作空间，请先前往P4创建
          </div>
          <BasicForm class="!my-6" @register="registerWorkForm" />
        </div>
      </div>
      <div class="w-full flex justify-center">
        <a-button type="primary" :loading="loading" @click="handleSaveBranch">
          保存分支信息
        </a-button>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { type PropType, onMounted, ref } from 'vue';
import { streamTypeOptions } from '../streams.data';
import { createBranchFormSchema } from './fastAddStream.data';
import type { StreamsListItem } from '/@/api/page/model/p4Model';
import { addStream, createStream, editStream } from '/@/api/page/p4';
import { BasicForm, useForm } from '/@/components/Form';
import { SvgIcon } from '/@/components/Icon/index';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { useP4Depot } from '/@/views/versionControl/p4PermissionManage/hook';
import { useTrack } from '/@/hooks/system/useTrack';
import type { ChangeEvent } from 'ant-design-vue/es/_util/EventInterface';

defineOptions({
  name: 'CreateBranch',
});

const props = defineProps({
  depotID: {
    type: Number,
    required: true,
  },
  isExistStream: {
    type: Boolean,
    default: false,
  },
  streamList: {
    type: Array as PropType<StreamsListItem[]>,
    default: () => [],
  },
});
const emit = defineEmits(['nextStep', 'streamTypeChange']);
const { prefixCls } = useDesign('create-branch');
const { getCurDepot } = useP4Depot();
const loading = ref(false);
const needWorkspace = ref(false);
const userStore = useUserStoreWithOut();
const createdStream = ref<StreamsListItem>();

const [registerForm, { updateSchema, setFieldsValue, getFieldsValue, validate }] = useForm({
  labelWidth: 120,
  schemas: createBranchFormSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 22 },
});

const [registerWorkForm, { validate: workValidate }] = useForm({
  labelWidth: 120,
  schemas: [createBranchFormSchema.find((item) => item.field === 'workspace')!],
  showActionButtonGroup: false,
  baseColProps: { span: 22 },
});

function getStreamTypePath(streamType: number) {
  const curType = streamTypeOptions.find((item) => item.value === streamType);

  return curType?.path;
}

onMounted(() => {
  const curDepot = getCurDepot(props.depotID);

  updateSchema([
    {
      field: 'name',
      componentProps: {
        onChange: (event: ChangeEvent) => {
          setFieldsValue({
            path:
                event?.target?.value && getFieldsValue().streamType
                  ? `${curDepot?.prefix}${getFieldsValue().rootDirectory}/${event?.target?.value}`
                  : '',
          });
        },
      },
    },
    {
      field: 'streamType',
      componentProps: {
        onChange: (val: number) => {
          emit('streamTypeChange', val);
          setFieldsValue({
            rootDirectory: getStreamTypePath(val),
          });
        },
      },
    },
    {
      field: 'rootDirectory',
      defaultValue: getStreamTypePath(2),
      componentProps: {
        onChange: (event: ChangeEvent) => {
          const val = typeof event === 'string' ? event : event.target.value;

          setFieldsValue({
            path:
                val && getFieldsValue().name
                  ? `${curDepot?.prefix}${val}/${getFieldsValue().name}`
                  : '',
          });
        },
      },
    },
    {
      field: 'parent',
      ifShow: () => !props.isExistStream && getFieldsValue().streamType !== 1,
      componentProps: {
        options: props.streamList,
      },
    },
    {
      field: 'workspace',
      ifShow: () => props.isExistStream && getFieldsValue().streamType === 5,
    },
    {
      field: 'path',
      dynamicDisabled: () => !props.isExistStream,
      required: () => props.isExistStream,
    },
  ]);
});

async function handleCreateBranch() {
  if (loading.value) {
    return;
  }
  const values = await validate();

  loading.value = true;

  const submitData = Object.assign({ depotID: props.depotID }, values);

  const addApi = props.isExistStream ? addStream : createStream;

  if (!userStore.getProjectId) {
    return;
  }

  const res = await addApi(userStore.getProjectId, submitData);

  if (res?.id) {
    const { setTrack } = useTrack();

    setTrack(props.isExistStream ? '5cufhbxqmn' : 'm0kgvsznsp');

    if (!props.isExistStream && values.streamType === 5) {
      needWorkspace.value = true;
      createdStream.value = {
        ID: res?.id,
        ...submitData,
      };
    } else {
      emit('nextStep', { id: res?.id, status: 1 });
    }
  }
  // 仅异常状态下关闭loading
  if (res?.code && res?.code !== 0) {
    loading.value = false;
  }
}

async function handleSaveBranch() {
  const values = await workValidate();

  loading.value = true;

  if (!userStore.getProjectId) {
    return;
  }

  const res = await editStream(
    userStore.getProjectId,
    {
      ...createdStream.value,
      workspace: values.workspace,
    },
    createdStream.value!.ID!,
  );

  emit('nextStep', { id: createdStream.value!.ID!, status: res?.code === 7 ? 2 : 1 });
  loading.value = false;
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-create-branch';
//  .@{prefix-cls} {
//  }
</style>
