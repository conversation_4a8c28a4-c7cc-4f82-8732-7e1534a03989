<template>
  <BasicModal
    v-bind="$attrs"
    title="通知配置"
    destroyOnClose
    okText="保存"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <div class="mt-2 flex justify-center">
      <div class="flex flex-col items-start">
        <div>
          开启通知加急后, 对应审查卡片中的Reviewers会收到加急通知；
        </div>
        <div>
          屏蔽灰色通知后，非最终Reviewers将不会收到灰色通知。
        </div>
      </div>
    </div>
    <BasicForm class="mt-12" @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { reviewUrgentModalFormSchema } from '../swarmSettings.data.tsx';
import type { ReviewUrgentListItem } from '../../../../api/page/model/swarmModel';
import { addReview<PERSON>rgent, editReviewUrgent, getReviewUrgentListByPage } from '../../../../api/page/swarm';
import { BasicForm, useForm } from '../../../../components/Form';
import { type ModalMethods, BasicModal, useModalInner } from '../../../../components/Modal';
import { useTrack } from '../../../../hooks/system/useTrack';
import { useMessage } from '../../../../hooks/web/useMessage';
import { useUserStoreWithOut } from '../../../../store/modules/user';

const emit = defineEmits<{
  (event: 'register', modalMethod: ModalMethods, uuid: number): void;
}>();

const userStore = useUserStoreWithOut();
const { currentRoute } = useRouter();
const { createMessage } = useMessage();
const streamID = Number(currentRoute.value.params.stream_id);
const reviewUrgentInfo = ref<ReviewUrgentListItem>();

const [registerForm, { resetFields, validate, setFieldsValue, clearValidate }] = useForm({
  schemas: reviewUrgentModalFormSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 16, offset: 8 },
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async () => {
  setModalProps({ confirmLoading: true });
  await resetFields();
  await getReviewUrgentList();

  if (reviewUrgentInfo.value?.ID) {
    await setFieldsValue({
      ...reviewUrgentInfo.value,
    });
    await clearValidate();
  }

  setModalProps({ confirmLoading: false });
});

async function getReviewUrgentList() {
  const { list } = await getReviewUrgentListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 1,
    streamID,
  });

  reviewUrgentInfo.value = list?.[0];
}

async function handleSubmit() {
  try {
    const values = await validate();

    setModalProps({ confirmLoading: true });

    const submitData = {
      streamID,
      ...values,
    };
    let res: any;

    if (!reviewUrgentInfo.value?.ID) {
      res = await addReviewUrgent(userStore.getProjectId, submitData);
    } else {
      res = await editReviewUrgent(
        userStore.getProjectId,
        submitData,
        reviewUrgentInfo.value?.ID,
      );
    }

    if (res?.code === 7) {
      return;
    }

    createMessage.success('保存成功');

    const { setTrack } = useTrack();

    setTrack('ikxhx4nvxz');
    closeModal();
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
