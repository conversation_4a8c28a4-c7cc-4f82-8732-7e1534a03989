import { type PropType, computed, defineComponent, watch } from 'vue';
import { Button, message, Tooltip } from 'ant-design-vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../api';
import GroupUpdate from '../../assets/svg/GroupUpdate.svg?component';
import GroupAdd from '../../assets/svg/GroupAdd.svg?component';

export const TaskGroupButton = defineComponent({
  props: {
    projectId: Number as PropType<number | undefined>,
    ruleId: String as PropType<string | undefined>,
  },
  setup(props) {
    const { data: chatInfo, execute: fetchChatInfo, loading: loadingChatInfo } = useLatestPromise(mergeApi.v1.notifyGetInfo);
    const { execute: createChat, loading: creatingChat } = useLatestPromise(mergeApi.v1.notifyJoinChat);
    const chatId = computed(() => chatInfo.value?.data?.data?.chatId);

    watch([() => props.projectId, () => props.ruleId], ([pId, rId]) => {
      if (!pId || !rId) {
        return;
      }
      fetchChatInfo({ id: pId, ruleId: rId }, {});
    }, { immediate: true });

    async function handleCreateGroup() {
      if (!props.projectId || !props.ruleId) {
        return;
      }
      const res = await createChat({ id: props.projectId }, { ruleId: props.ruleId });
      if (res?.data?.data?.chatId) {
        window.open(`https://applink.feishu.cn/client/chat/open?openChatId=${res?.data?.data?.chatId}`, '_blank', 'noopener,noreferrer');
        message.success('请求已发送，请至飞书查看通知');
        await fetchChatInfo({ id: props.projectId, ruleId: props.ruleId }, {});
      }
    }

    return () => {
      if (chatId.value) {
        return (
          <span class="inline-flex items-center">
            <span class="mr-10px c-FO-Content-Text2">当前已存在群聊</span>
            <Tooltip title="点击在更新群聊成员" trigger="hover">
              <Button class="btn-fill-secondary" loading={creatingChat.value} onClick={handleCreateGroup}>
                {{
                  icon: () => <GroupUpdate class="font-size-16px" />,
                  default: () => '更新群聊成员',
                }}
              </Button>
            </Tooltip>
          </span>
        );
      }

      if (!loadingChatInfo.value) {
        return (
          <Tooltip title="点击在飞书创建群聊" trigger="hover">
            <Button class="btn-fill-secondary" loading={creatingChat.value} onClick={handleCreateGroup}>
              {{
                icon: () => <GroupAdd class="font-size-16px" />,
                default: () => '一键拉群',
              }}
            </Button>
          </Tooltip>
        );
      }
    };
  },
});
