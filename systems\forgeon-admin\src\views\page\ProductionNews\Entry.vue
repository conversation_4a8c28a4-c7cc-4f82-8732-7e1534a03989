<template>
  <PageContainer class="flex-auto overflow-auto">
    <div class="mb-[12px] flex items-center justify-between">
      <div class="font-bold">
        产品动态列表
      </div>
      <Button type="primary" @click="handleAddNews">
        添加
      </Button>
    </div>
    <Table
      :rowKey="record => record.id"
      :data-source="renderTableData"
      :columns="tableColumn"
      :loading="loading"
      :pagination="paginationProp"
      @change="onTableChange"
    />
    <FormModalHolder />
  </PageContainer>
</template>

<script setup lang="tsx">
import { type TableProps, Button, message, Popconfirm, Table } from 'ant-design-vue';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import PageContainer from '../../../components/Framework/PageContainer.vue';
import { forgeonAdminApi } from '../../../api';
import { computed } from 'vue';
import NewsFormModal from './NewsFormModal.vue';
import type { ArrayWrappedType } from '@hg-tech/utils';
import dayjs from 'dayjs';
import { useAntdTable } from '../../../composition/useAntdTable.ts';

const [FormModalHolder, show] = useModalShow(NewsFormModal);
const { data, loading, execute } = useLatestPromise(forgeonAdminApi.api.configServiceListConfigNews);
const renderTableData = computed(() => data.value?.data?.data?.list ?? []);

const { onTableChange, refreshTableData, paginationProp } = useAntdTable<'publish_time'>(
  (pageInfo, sortInfo) => execute({ ...pageInfo, ...sortInfo }, {}).then((res) => {
    const data = res?.data?.data;
    if (data) {
      return {
        ...data,
        total: Number(data.total) || 0,
      };
    }
    return data;
  }),
  { immediate: true },
);

const tableColumn = computed<TableProps['columns']>(() => [
  {
    title: '标题',
    width: 250,
    dataIndex: 'title',
  },
  {
    title: '分类',
    width: 180,
    dataIndex: 'category',
  },
  {
    title: '发布时间',
    width: 180,
    key: 'publish_time',
    sorter: true,
    customRender({ record }) {
      return dayjs(Number(record.publishTime)).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '跳转链接',
    width: 200,
    customRender({ record }) {
      return <a class="break-all" href={record.url} target="_blank">{record.url}</a>;
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 180,
    customRender({ record }) {
      return (
        <div>
          <Button onClick={() => handleEditNews(record)} type="link">编辑</Button>
          <Popconfirm
            ok-text="删除"
            okButtonProps={{ danger: true }}
            onConfirm={() => handleDeleteNews(record)}
            title="确认要删除吗？"
          >
            <Button danger type="link">删除</Button>
          </Popconfirm>
        </div>
      );
    },
  },
]);
async function handleAddNews() {
  await show({
    title: '添加产品动态',
    sentReq(formValue) {
      return forgeonAdminApi.api.configServiceCreateConfigNews({}, {
        ...formValue,
        publishTime: formValue.publishTime?.valueOf()?.toString(),
      });
    },
  });
  message.success('添加成功');
  return refreshTableData();
}
async function handleEditNews(row: ArrayWrappedType<typeof renderTableData.value>) {
  await show({
    title: '编辑产品动态',
    initData: {
      ...row,
      publishTime: dayjs(Number(row.publishTime)),
    },
    sentReq(formValue) {
      return forgeonAdminApi.api.configServiceUpdateConfigNews({ id: String(row.id) }, {
        ...formValue,
        publishTime: formValue.publishTime?.valueOf()?.toString(),
      });
    },
  });
  message.success('编辑成功');
  return refreshTableData();
}
async function handleDeleteNews(row: ArrayWrappedType<typeof renderTableData.value>) {
  await forgeonAdminApi.api.configServiceDeleteConfigNews({ id: String(row.id) }, {});
  message.success('删除成功');
  return refreshTableData();
}
</script>
